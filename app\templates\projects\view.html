{% extends "base/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>{{ project.name }}</h1>
        <p class="text-muted">{{ project.get_type_display() }} - {{ project.get_status_display() }}</p>
    </div>
    <div class="col-md-4 text-left">
        {% if current_user.can_edit_project(project) %}
            <a href="{{ url_for('projects.edit', id=project.id) }}" class="btn btn-secondary">تعديل المشروع</a>
            <a href="{{ url_for('projects.delete', id=project.id) }}" class="btn btn-danger">حذف المشروع</a>
        {% endif %}
    </div>
</div>

<!-- Project Details -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>تفاصيل المشروع</h5>
            </div>
            <div class="card-body">
                {% if project.description %}
                    <p><strong>الوصف:</strong></p>
                    <p>{{ project.description }}</p>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الموقع:</strong> {{ project.location or 'لم يتم تحديده' }}</p>
                        <p><strong>نوع المشروع:</strong> {{ project.get_type_display() }}</p>
                        <p><strong>حالة المشروع:</strong> {{ project.get_status_display() }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المساحة:</strong> {{ "{:,.0f}".format(project.area) if project.area else "غير محدد" }} {% if project.area %}م²{% endif %}</p>
                        <p><strong>عدد الطوابق:</strong> {{ project.floors }}</p>
                        <p><strong>الميزانية:</strong> {{ "{:,.2f}".format(project.budget) if project.budget else "غير محدد" }} {% if project.budget %}ريال{% endif %}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ project.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>آخر تحديث:</strong> {{ project.updated_at.strftime('%Y/%m/%d %H:%M') if project.updated_at else project.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mt-3">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <strong>تقدم المشروع</strong>
                        <span class="badge" style="background-color: var(--primary-color); color: white;">{{ project.get_progress_percentage() }}%</span>
                    </div>
                    <div style="background-color: #f8f9fa; border-radius: 0.25rem; height: 1rem; overflow: hidden;">
                        <div style="background-color: var(--primary-color); height: 100%; width: {{ project.get_progress_percentage() }}%; transition: width 0.3s ease;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>إحصائيات المشروع</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <h3 style="color: var(--primary-color);">{{ drawings_count }}</h3>
                    <p class="text-muted">عدد الرسوم</p>
                </div>
                <div class="mb-3">
                    <h3 style="color: var(--accent-color);">{{ calculations_count }}</h3>
                    <p class="text-muted">عدد الحسابات</p>
                </div>
                {% if latest_drawing %}
                    <div class="mb-3">
                        <p class="text-muted">آخر رسم:</p>
                        <p><strong>{{ latest_drawing.name }}</strong></p>
                        <small class="text-muted">{{ latest_drawing.created_at.strftime('%Y/%m/%d') }}</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>الإجراءات السريعة</h5>
            </div>
            <div class="card-body text-center">
                {% if current_user.role in ['Engineer', 'Supervisor'] %}
                    <a href="{{ url_for('drawings.create') }}?project_id={{ project.id }}" class="btn btn-primary mr-2">إنشاء رسم جديد</a>
                    <a href="{{ url_for('calculations.create') }}?project_id={{ project.id }}" class="btn btn-secondary mr-2">إنشاء حساب جديد</a>
                {% endif %}
                <a href="{{ url_for('exports.pdf_report', project_id=project.id) }}" class="btn btn-info mr-2">تصدير تقرير PDF</a>
                <a href="{{ url_for('projects.index') }}" class="btn btn-outline-primary">العودة للمشاريع</a>
            </div>
        </div>
    </div>
</div>

<!-- Project Files/Drawings -->
{% if drawings_count > 0 %}
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>رسوم المشروع</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">سيتم عرض رسوم المشروع هنا عند اكتمال تطوير وحدة الرسوم.</p>
                <a href="{{ url_for('drawings.index') }}?project_id={{ project.id }}" class="btn btn-primary">عرض جميع الرسوم</a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
