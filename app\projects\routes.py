# -*- coding: utf-8 -*-
"""
Project Routes
مسارات المشاريع
"""

from flask import render_template, redirect, url_for, flash, request, abort
from flask_login import login_required, current_user
from models import db, Project, User
from app.projects import bp
from app.projects.forms import ProjectForm, ProjectSearchForm, ProjectDeleteForm

@bp.route('/')
@login_required
def index():
    """Projects list - قائمة المشاريع"""
    search_form = ProjectSearchForm()
    
    # Base query based on user role
    if current_user.role == 'Supervisor':
        # Supervisors can see all projects
        query = Project.query
    else:
        # Engineers and Clients see only their projects
        query = current_user.projects
    
    # Apply search filters
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(Project.name.contains(search_term) | 
                           Project.location.contains(search_term) |
                           Project.description.contains(search_term))
        search_form.search.data = search_term
    
    if request.args.get('project_type'):
        project_type = request.args.get('project_type')
        query = query.filter(Project.project_type == project_type)
        search_form.project_type.data = project_type
    
    if request.args.get('status'):
        status = request.args.get('status')
        query = query.filter(Project.status == status)
        search_form.status.data = status
    
    # Pagination
    page = request.args.get('page', 1, type=int)
    per_page = 10
    projects = query.order_by(Project.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('projects/index.html', 
                         title='المشاريع', 
                         projects=projects, 
                         search_form=search_form)

@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create new project - إنشاء مشروع جديد"""
    if current_user.role not in ['Engineer', 'Supervisor']:
        flash('ليس لديك صلاحية لإنشاء مشاريع جديدة.', 'error')
        return redirect(url_for('projects.index'))
    
    form = ProjectForm()
    
    if form.validate_on_submit():
        project = Project(
            name=form.name.data,
            user_id=current_user.id,
            location=form.location.data,
            description=form.description.data,
            project_type=form.project_type.data
        )
        
        project.status = form.status.data
        project.budget = form.budget.data
        project.area = form.area.data
        project.floors = form.floors.data
        project.is_public = form.is_public.data
        
        db.session.add(project)
        db.session.commit()
        
        flash(f'تم إنشاء المشروع "{project.name}" بنجاح!', 'success')
        return redirect(url_for('projects.view', id=project.id))
    
    return render_template('projects/create.html', title='إنشاء مشروع جديد', form=form)

@bp.route('/<int:id>')
@login_required
def view(id):
    """View project details - عرض تفاصيل المشروع"""
    project = Project.query.get_or_404(id)
    
    # Check permissions
    if not current_user.can_view_project(project):
        flash('ليس لديك صلاحية لعرض هذا المشروع.', 'error')
        return redirect(url_for('projects.index'))
    
    # Get project statistics
    drawings_count = project.drawings.count()
    calculations_count = project.calculations.count()
    latest_drawing = project.get_latest_drawing()
    
    return render_template('projects/view.html', 
                         title=project.name, 
                         project=project,
                         drawings_count=drawings_count,
                         calculations_count=calculations_count,
                         latest_drawing=latest_drawing)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit project - تعديل المشروع"""
    project = Project.query.get_or_404(id)
    
    # Check permissions
    if not current_user.can_edit_project(project):
        flash('ليس لديك صلاحية لتعديل هذا المشروع.', 'error')
        return redirect(url_for('projects.view', id=id))
    
    form = ProjectForm(obj=project)
    
    if form.validate_on_submit():
        project.name = form.name.data
        project.location = form.location.data
        project.description = form.description.data
        project.project_type = form.project_type.data
        project.status = form.status.data
        project.budget = form.budget.data
        project.area = form.area.data
        project.floors = form.floors.data
        project.is_public = form.is_public.data
        
        db.session.commit()
        
        flash(f'تم تحديث المشروع "{project.name}" بنجاح!', 'success')
        return redirect(url_for('projects.view', id=project.id))
    
    return render_template('projects/edit.html', 
                         title=f'تعديل المشروع: {project.name}', 
                         form=form, 
                         project=project)

@bp.route('/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def delete(id):
    """Delete project - حذف المشروع"""
    project = Project.query.get_or_404(id)
    
    # Check permissions
    if not current_user.can_edit_project(project):
        flash('ليس لديك صلاحية لحذف هذا المشروع.', 'error')
        return redirect(url_for('projects.view', id=id))
    
    form = ProjectDeleteForm()
    
    if form.validate_on_submit():
        project_name = project.name
        db.session.delete(project)
        db.session.commit()
        
        flash(f'تم حذف المشروع "{project_name}" نهائياً.', 'info')
        return redirect(url_for('projects.index'))
    
    return render_template('projects/delete.html', 
                         title=f'حذف المشروع: {project.name}', 
                         form=form, 
                         project=project)
