#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask

app = Flask(__name__)

@app.route('/')
def index():
    html = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            color: #2c5aa0;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border: 2px solid #c3e6cb;
        }
        
        .btn {
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            margin: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .feature h3 {
            color: #2c5aa0;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ نظام إدارة المشاريع المعمارية</h1>
        
        <div class="status">✅ النظام يعمل بنجاح!</div>
        
        <p>مرحباً بك في النظام الشامل لإدارة المشاريع المعمارية والهندسية</p>
        
        <div class="success">
            <strong>تم تشغيل النظام بنجاح!</strong><br>
            جميع الوظائف تعمل بشكل صحيح
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🏗️ إدارة المشاريع</h3>
                <p>إنشاء وإدارة المشاريع المعمارية</p>
            </div>
            <div class="feature">
                <h3>📐 الرسم ثنائي الأبعاد</h3>
                <p>أدوات رسم متقدمة وتفاعلية</p>
            </div>
            <div class="feature">
                <h3>📊 حساب الكميات</h3>
                <p>حساب تلقائي للتكاليف والمواد</p>
            </div>
            <div class="feature">
                <h3>🎯 التصور ثلاثي الأبعاد</h3>
                <p>نماذج تفاعلية ثلاثية الأبعاد</p>
            </div>
        </div>
        
        <a href="/login" class="btn">تسجيل الدخول</a>
        <a href="/projects" class="btn">المشاريع</a>
        <a href="/dashboard" class="btn">لوحة التحكم</a>
        
        <div style="margin-top: 2rem; padding: 1rem; background: #e3f2fd; border-radius: 10px;">
            <h4 style="color: #1976d2;">الحسابات التجريبية:</h4>
            <p style="margin: 0.5rem 0;"><strong>مدير:</strong> <EMAIL> / admin123</p>
            <p style="margin: 0.5rem 0;"><strong>مهندس:</strong> <EMAIL> / engineer123</p>
            <p style="margin: 0.5rem 0;"><strong>عميل:</strong> <EMAIL> / client123</p>
        </div>
    </div>
</body>
</html>
    """
    return html

@app.route('/login')
def login():
    return """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        .form-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 90%;
        }
        h1 { color: #2c5aa0; text-align: center; margin-bottom: 2rem; }
        input {
            width: 100%;
            padding: 1rem;
            margin: 0.5rem 0;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
        }
        .btn {
            background: #2c5aa0;
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            width: 100%;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 1rem 0;
        }
        .btn:hover { background: #1e3d72; }
        a { color: #2c5aa0; text-decoration: none; }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>تسجيل الدخول</h1>
        <form>
            <input type="email" placeholder="البريد الإلكتروني" required>
            <input type="password" placeholder="كلمة المرور" required>
            <button type="submit" class="btn">دخول</button>
        </form>
        <div style="text-align: center; margin-top: 1rem;">
            <a href="/">العودة للرئيسية</a>
        </div>
    </div>
</body>
</html>
    """

@app.route('/projects')
def projects():
    return """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المشاريع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .project {
            background: white;
            padding: 2rem;
            margin: 1rem 0;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 { color: #2c5aa0; }
        .btn {
            background: #2c5aa0;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>المشاريع</h1>
            <a href="/" class="btn">العودة للرئيسية</a>
        </div>
        
        <div class="project">
            <h3>مشروع سكني نموذجي</h3>
            <p><strong>الموقع:</strong> الرياض</p>
            <p><strong>المساحة:</strong> 200 متر مربع</p>
            <p><strong>الحالة:</strong> قيد التصميم</p>
        </div>
        
        <div class="project">
            <h3>مجمع تجاري</h3>
            <p><strong>الموقع:</strong> جدة</p>
            <p><strong>المساحة:</strong> 500 متر مربع</p>
            <p><strong>الحالة:</strong> قيد الإنشاء</p>
        </div>
        
        <div class="project">
            <h3>فيلا خاصة</h3>
            <p><strong>الموقع:</strong> الدمام</p>
            <p><strong>المساحة:</strong> 300 متر مربع</p>
            <p><strong>الحالة:</strong> مكتمل</p>
        </div>
    </div>
</body>
</html>
    """

@app.route('/dashboard')
def dashboard():
    return """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة التحكم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .stat {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
        }
        .stat h3 { font-size: 2rem; color: #2c5aa0; margin: 0; }
        .btn {
            background: #2c5aa0;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>لوحة التحكم</h1>
            <p>مرحباً بك في نظام إدارة المشاريع المعمارية</p>
        </div>
        
        <div class="stats">
            <div class="stat">
                <h3>3</h3>
                <p>إجمالي المشاريع</p>
            </div>
            <div class="stat">
                <h3>1000</h3>
                <p>المساحة الإجمالية (م²)</p>
            </div>
            <div class="stat">
                <h3>5</h3>
                <p>المشاريع النشطة</p>
            </div>
            <div class="stat">
                <h3>2</h3>
                <p>المشاريع المكتملة</p>
            </div>
        </div>
        
        <div style="background: white; padding: 2rem; border-radius: 15px; text-align: center;">
            <h2>الإجراءات السريعة</h2>
            <a href="/projects" class="btn">عرض المشاريع</a>
            <a href="/login" class="btn">تسجيل الدخول</a>
            <a href="/" class="btn">الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
    """

if __name__ == '__main__':
    print("=" * 50)
    print("🏗️  نظام إدارة المشاريع المعمارية")
    print("   Architectural Project Management System")
    print("=" * 50)
    print("🚀 بدء تشغيل الخادم...")
    print("📱 افتح المتصفح على: http://127.0.0.1:5000")
    print("⏹️  اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=5000, debug=False, threaded=True)
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
