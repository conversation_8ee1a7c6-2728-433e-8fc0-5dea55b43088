#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for Architectural Project Management System
سكريبت إعداد نظام إدارة المشاريع المعمارية
"""

import os
import sys
import subprocess
from app import create_app
from models import db, User, Project, Drawing, Calculation

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    print("Dependencies installed successfully!")

def create_database():
    """Create database tables"""
    print("Creating database...")
    app = create_app()
    with app.app_context():
        db.create_all()
        print("Database created successfully!")

def create_sample_data():
    """Create sample data for testing"""
    print("Creating sample data...")
    app = create_app()
    with app.app_context():
        # Check if admin user already exists
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            # Create admin user
            admin = User(
                name='مدير النظام',
                email='<EMAIL>',
                password='admin123',
                role='Supervisor'
            )
            db.session.add(admin)
            
            # Create engineer user
            engineer = User(
                name='أحمد المهندس',
                email='<EMAIL>',
                password='engineer123',
                role='Engineer'
            )
            db.session.add(engineer)
            
            # Create client user
            client = User(
                name='محمد العميل',
                email='<EMAIL>',
                password='client123',
                role='Client'
            )
            db.session.add(client)
            
            db.session.commit()
            
            # Create sample project
            project = Project(
                name='مشروع سكني نموذجي',
                user_id=engineer.id,
                location='الرياض، المملكة العربية السعودية',
                description='مشروع سكني نموذجي يتكون من فيلا بطابقين مع حديقة',
                project_type='Residential'
            )
            project.status = 'Design'
            project.budget = 500000.00
            project.area = 300.00
            project.floors = 2
            project.is_public = True
            
            db.session.add(project)
            db.session.commit()
            
            print("Sample data created successfully!")
            print("\nSample users created:")
            print("Admin: <EMAIL> / admin123")
            print("Engineer: <EMAIL> / engineer123")
            print("Client: <EMAIL> / client123")
        else:
            print("Sample data already exists!")

def run_tests():
    """Run system tests"""
    print("Running system tests...")
    try:
        subprocess.check_call([sys.executable, "test_system.py"])
        print("All tests passed!")
        return True
    except subprocess.CalledProcessError:
        print("Some tests failed. Please check the output above.")
        return False

def main():
    """Main setup function"""
    print("=== Architectural Project Management System Setup ===")
    print("=== إعداد نظام إدارة المشاريع المعمارية ===\n")

    try:
        # Install dependencies
        install_dependencies()

        # Create database
        create_database()

        # Create sample data
        create_sample_data()

        print("\n=== Setup completed successfully! ===")
        print("=== تم الإعداد بنجاح! ===")

        # Ask if user wants to run tests
        run_tests_choice = input("\nDo you want to run system tests? (y/n): ").lower()
        if run_tests_choice == 'y':
            if run_tests():
                print("✓ All tests passed!")
            else:
                print("⚠ Some tests failed, but setup is complete.")

        print("\nTo run the application:")
        print("python simple_app.py")
        print("\nThen open your browser and go to:")
        print("http://localhost:5000")

        print("\nDemo accounts:")
        print("Admin: <EMAIL> / admin123")
        print("Engineer: <EMAIL> / engineer123")
        print("Client: <EMAIL> / client123")

    except Exception as e:
        print(f"Setup failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
