{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-8">
        <h1>حساب الكميات والتكاليف</h1>
        <p class="text-muted">حساب تلقائي للمواد والكميات وتقديرات التكلفة</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-primary" onclick="calculateFromDrawing()">حساب من الرسم</button>
        <button class="btn btn-success" onclick="saveCalculation()">حفظ الحساب</button>
    </div>
</div>

<!-- Input Form -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>بيانات المشروع</h6>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">اسم المشروع</label>
                    <input type="text" id="project-name" class="form-control" value="مشروع تجريبي">
                </div>
                <div class="form-group">
                    <label class="form-label">المساحة الإجمالية (م²)</label>
                    <input type="number" id="total-area" class="form-control" value="200" onchange="calculateManually()">
                </div>
                <div class="form-group">
                    <label class="form-label">عدد الطوابق</label>
                    <input type="number" id="floors" class="form-control" value="1" onchange="calculateManually()">
                </div>
                <div class="form-group">
                    <label class="form-label">ارتفاع الطابق (م)</label>
                    <input type="number" id="floor-height" class="form-control" value="3" onchange="calculateManually()">
                </div>
                <button class="btn btn-secondary btn-sm" onclick="calculateManually()">حساب يدوي</button>
            </div>
        </div>
    </div>
    
    <!-- Results -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6>نتائج الحساب</h6>
            </div>
            <div class="card-body">
                <div id="calculation-results">
                    <p class="text-muted">أدخل بيانات المشروع أو احسب من الرسم لعرض النتائج</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Report -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col">
                        <h6>تقرير مفصل للكميات والتكاليف</h6>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" onclick="exportReport('json')">تصدير JSON</button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportReport('csv')">تصدير CSV</button>
                        <button class="btn btn-outline-info btn-sm" onclick="printReport()">طباعة</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="detailed-report">
                    <p class="text-muted">سيظهر التقرير المفصل هنا بعد إجراء الحساب</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Saved Calculations -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>الحسابات المحفوظة</h6>
            </div>
            <div class="card-body">
                <div id="saved-calculations">
                    <p class="text-muted">لا توجد حسابات محفوظة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Material Prices Modal -->
<div class="modal fade" id="pricesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">أسعار المواد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الوحدة</th>
                                <th>السعر (ريال)</th>
                                <th>تعديل</th>
                            </tr>
                        </thead>
                        <tbody id="prices-table">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="updatePrices()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/quantity-calculator.js') }}"></script>

<script>
// Initialize calculator
let calculator;
let currentReport = null;

document.addEventListener('DOMContentLoaded', function() {
    calculator = new QuantityCalculator();
    loadSavedCalculations();
    
    // Load default calculation
    calculateManually();
});

function calculateFromDrawing() {
    // Check if drawing canvas exists (from drawing page)
    if (typeof drawingCanvas !== 'undefined') {
        const objects = drawingCanvas.canvas.getObjects().filter(obj => !obj.excludeFromExport);
        const areas = calculator.calculateAreasFromDrawing(objects);
        const floors = parseInt(document.getElementById('floors').value) || 1;
        const quantities = calculator.calculateMaterialQuantities(areas, floors);
        
        displayResults(areas, quantities);
    } else {
        alert('يجب أن تكون في صفحة الرسم لاستخدام هذه الميزة');
    }
}

function calculateManually() {
    const totalArea = parseFloat(document.getElementById('total-area').value) || 0;
    const floors = parseInt(document.getElementById('floors').value) || 1;
    const floorHeight = parseFloat(document.getElementById('floor-height').value) || 3;
    
    // Create mock areas object
    const areas = {
        totalArea: totalArea,
        roomAreas: [{
            name: 'المساحة الإجمالية',
            area: totalArea,
            width: Math.sqrt(totalArea),
            height: Math.sqrt(totalArea)
        }],
        wallAreas: [{
            length: Math.sqrt(totalArea) * 4, // محيط تقريبي
            area: Math.sqrt(totalArea) * 4 * floorHeight,
            height: floorHeight
        }],
        openingAreas: []
    };
    
    const quantities = calculator.calculateMaterialQuantities(areas, floors);
    displayResults(areas, quantities);
}

function displayResults(areas, quantities) {
    const projectInfo = {
        name: document.getElementById('project-name').value,
        floors: parseInt(document.getElementById('floors').value) || 1
    };
    
    currentReport = calculator.generateDetailedReport(areas, quantities, projectInfo);
    
    // Display summary
    const resultsDiv = document.getElementById('calculation-results');
    let html = '<div class="row">';
    
    // Key metrics
    html += '<div class="col-md-3 text-center mb-3">';
    html += '<h4 style="color: var(--primary-color);">' + areas.totalArea.toFixed(2) + '</h4>';
    html += '<p class="text-muted">المساحة الإجمالية (م²)</p>';
    html += '</div>';
    
    html += '<div class="col-md-3 text-center mb-3">';
    html += '<h4 style="color: var(--accent-color);">' + quantities.concrete.total.toFixed(2) + '</h4>';
    html += '<p class="text-muted">الخرسانة (م³)</p>';
    html += '</div>';
    
    html += '<div class="col-md-3 text-center mb-3">';
    html += '<h4 style="color: var(--info-color);">' + quantities.steel.weight.toFixed(0) + '</h4>';
    html += '<p class="text-muted">الحديد (كجم)</p>';
    html += '</div>';
    
    html += '<div class="col-md-3 text-center mb-3">';
    html += '<h4 style="color: var(--warning-color);">' + currentReport.costs.total.toLocaleString('ar-SA') + '</h4>';
    html += '<p class="text-muted">التكلفة الإجمالية (ريال)</p>';
    html += '</div>';
    
    html += '</div>';
    resultsDiv.innerHTML = html;
    
    // Display detailed report
    displayDetailedReport(currentReport);
}

function displayDetailedReport(report) {
    const reportDiv = document.getElementById('detailed-report');
    
    let html = '<div class="row mb-4">';
    html += '<div class="col-md-6">';
    html += '<h6>معلومات المشروع</h6>';
    html += '<p><strong>اسم المشروع:</strong> ' + report.projectInfo.name + '</p>';
    html += '<p><strong>التاريخ:</strong> ' + report.projectInfo.date + '</p>';
    html += '<p><strong>المساحة الإجمالية:</strong> ' + report.projectInfo.totalArea + ' م²</p>';
    html += '<p><strong>عدد الطوابق:</strong> ' + report.projectInfo.floors + '</p>';
    html += '</div>';
    html += '<div class="col-md-6">';
    html += '<h6>ملخص التكاليف</h6>';
    html += '<p><strong>المجموع الفرعي:</strong> ' + report.costs.subtotal.toLocaleString('ar-SA') + ' ريال</p>';
    html += '<p><strong>ضريبة القيمة المضافة (15%):</strong> ' + report.costs.vat.toLocaleString('ar-SA') + ' ريال</p>';
    html += '<p><strong>المجموع الإجمالي:</strong> ' + report.costs.total.toLocaleString('ar-SA') + ' ريال</p>';
    html += '</div>';
    html += '</div>';
    
    // Materials table
    html += '<div class="table-responsive">';
    html += '<table class="table table-striped">';
    html += '<thead><tr><th>المادة</th><th>الكمية</th><th>الوحدة</th><th>سعر الوحدة</th><th>التكلفة الإجمالية</th></tr></thead>';
    html += '<tbody>';
    
    Object.values(report.quantities).forEach(item => {
        html += '<tr>';
        html += '<td>' + item.name + '</td>';
        html += '<td>' + item.quantity + '</td>';
        html += '<td>' + item.unit + '</td>';
        html += '<td>' + item.unitPrice + ' ريال</td>';
        html += '<td>' + parseFloat(item.totalCost).toLocaleString('ar-SA') + ' ريال</td>';
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    html += '</div>';
    
    reportDiv.innerHTML = html;
}

function saveCalculation() {
    if (!currentReport) {
        alert('يجب إجراء حساب أولاً');
        return;
    }
    
    const name = prompt('أدخل اسم الحساب:') || 'حساب ' + new Date().toLocaleDateString('ar-SA');
    calculator.saveCalculation(name, currentReport);
    loadSavedCalculations();
    alert('تم حفظ الحساب بنجاح!');
}

function loadSavedCalculations() {
    const calculations = calculator.loadCalculations();
    const savedDiv = document.getElementById('saved-calculations');
    
    if (calculations.length === 0) {
        savedDiv.innerHTML = '<p class="text-muted">لا توجد حسابات محفوظة</p>';
        return;
    }
    
    let html = '<div class="row">';
    calculations.forEach(calc => {
        html += '<div class="col-md-4 mb-3">';
        html += '<div class="card">';
        html += '<div class="card-body">';
        html += '<h6>' + calc.name + '</h6>';
        html += '<p class="text-muted">' + new Date(calc.date).toLocaleDateString('ar-SA') + '</p>';
        html += '<p><strong>التكلفة:</strong> ' + calc.report.costs.total.toLocaleString('ar-SA') + ' ريال</p>';
        html += '<button class="btn btn-primary btn-sm" onclick="loadCalculation(' + calc.id + ')">تحميل</button> ';
        html += '<button class="btn btn-danger btn-sm" onclick="deleteCalculation(' + calc.id + ')">حذف</button>';
        html += '</div></div></div>';
    });
    html += '</div>';
    
    savedDiv.innerHTML = html;
}

function loadCalculation(id) {
    const calculation = calculator.calculations.find(calc => calc.id === id);
    if (calculation) {
        currentReport = calculation.report;
        document.getElementById('project-name').value = calculation.report.projectInfo.name;
        document.getElementById('total-area').value = calculation.report.projectInfo.totalArea;
        document.getElementById('floors').value = calculation.report.projectInfo.floors;
        
        displayDetailedReport(currentReport);
        
        // Update summary
        const resultsDiv = document.getElementById('calculation-results');
        let html = '<div class="row">';
        html += '<div class="col text-center">';
        html += '<h4 style="color: var(--primary-color);">تم تحميل الحساب: ' + calculation.name + '</h4>';
        html += '<p class="text-muted">التكلفة الإجمالية: ' + calculation.report.costs.total.toLocaleString('ar-SA') + ' ريال</p>';
        html += '</div></div>';
        resultsDiv.innerHTML = html;
    }
}

function deleteCalculation(id) {
    if (confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
        calculator.deleteCalculation(id);
        loadSavedCalculations();
    }
}

function exportReport(format) {
    if (!currentReport) {
        alert('يجب إجراء حساب أولاً');
        return;
    }
    
    let data, filename, mimeType;
    
    if (format === 'json') {
        data = calculator.exportReportAsJSON(currentReport);
        filename = 'calculation-report.json';
        mimeType = 'application/json';
    } else if (format === 'csv') {
        data = calculator.exportReportAsCSV(currentReport);
        filename = 'calculation-report.csv';
        mimeType = 'text/csv';
    }
    
    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function printReport() {
    if (!currentReport) {
        alert('يجب إجراء حساب أولاً');
        return;
    }
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تقرير حساب الكميات</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .header { text-align: center; margin-bottom: 30px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير حساب الكميات والتكاليف</h1>
                <h2>${currentReport.projectInfo.name}</h2>
                <p>التاريخ: ${currentReport.projectInfo.date}</p>
            </div>
            ${document.getElementById('detailed-report').innerHTML}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}
</script>
{% endblock %}
