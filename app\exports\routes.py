# -*- coding: utf-8 -*-
"""
Export Routes (Placeholder)
مسارات التصدير (مؤقت)
"""

from flask import render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from app.exports import bp

@bp.route('/')
@login_required
def index():
    """Exports list - قائمة التصديرات"""
    flash('صفحة التصدير قيد التطوير', 'info')
    return redirect(url_for('main.dashboard'))

@bp.route('/pdf/<int:project_id>')
@login_required
def pdf_report(project_id):
    """Export PDF report - تصدير تقرير PDF"""
    flash('تصدير PDF قيد التطوير', 'info')
    return redirect(url_for('projects.view', id=project_id))

@bp.route('/dxf/<int:drawing_id>')
@login_required
def dxf_export(drawing_id):
    """Export DXF file - تصدير ملف DXF"""
    flash('تصدير DXF قيد التطوير', 'info')
    return redirect(url_for('main.dashboard'))
