from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>نظام إدارة المشاريع المعمارية</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                direction: rtl;
                text-align: center;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 50px;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                max-width: 800px;
                margin: 0 auto;
            }
            h1 {
                font-size: 3em;
                margin-bottom: 20px;
            }
            p {
                font-size: 1.5em;
                margin: 20px 0;
            }
            .btn {
                background: #2c5aa0;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                font-size: 1.2em;
                margin: 10px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
            .btn:hover {
                background: #1e3d72;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏗️ نظام إدارة المشاريع المعمارية</h1>
            <p>مرحباً بك في النظام</p>
            <p>النظام يعمل بنجاح!</p>
            <a href="/login" class="btn">تسجيل الدخول</a>
            <a href="/projects" class="btn">المشاريع</a>
            <a href="/about" class="btn">حول النظام</a>
        </div>
    </body>
    </html>
    '''

@app.route('/login')
def login():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                direction: rtl;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 50px;
                margin: 0;
            }
            .form-container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                max-width: 500px;
                margin: 0 auto;
                text-align: center;
            }
            input {
                width: 100%;
                padding: 15px;
                margin: 10px 0;
                border: none;
                border-radius: 10px;
                font-size: 1.1em;
                text-align: right;
            }
            .btn {
                background: #2c5aa0;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                font-size: 1.2em;
                cursor: pointer;
                width: 100%;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="form-container">
            <h1>تسجيل الدخول</h1>
            <form>
                <input type="email" placeholder="البريد الإلكتروني" required>
                <input type="password" placeholder="كلمة المرور" required>
                <button type="submit" class="btn">دخول</button>
            </form>
            <p>الحسابات التجريبية:</p>
            <p><EMAIL> / admin123</p>
            <a href="/" style="color: white;">العودة للرئيسية</a>
        </div>
    </body>
    </html>
    '''

@app.route('/projects')
def projects():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>المشاريع</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                direction: rtl;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 50px;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                max-width: 1000px;
                margin: 0 auto;
            }
            .project {
                background: rgba(255,255,255,0.2);
                padding: 20px;
                margin: 20px 0;
                border-radius: 15px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>المشاريع</h1>
            <div class="project">
                <h3>مشروع سكني نموذجي</h3>
                <p>الموقع: الرياض</p>
                <p>المساحة: 200 متر مربع</p>
                <p>الحالة: قيد التصميم</p>
            </div>
            <div class="project">
                <h3>مجمع تجاري</h3>
                <p>الموقع: جدة</p>
                <p>المساحة: 500 متر مربع</p>
                <p>الحالة: قيد الإنشاء</p>
            </div>
            <div class="project">
                <h3>فيلا خاصة</h3>
                <p>الموقع: الدمام</p>
                <p>المساحة: 300 متر مربع</p>
                <p>الحالة: مكتمل</p>
            </div>
            <a href="/" style="color: white; font-size: 1.2em;">العودة للرئيسية</a>
        </div>
    </body>
    </html>
    '''

@app.route('/about')
def about():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>حول النظام</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                direction: rtl;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 50px;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                max-width: 800px;
                margin: 0 auto;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>حول النظام</h1>
            <h2>نظام إدارة المشاريع المعمارية</h2>
            <p>نظام شامل لإدارة المشاريع المعمارية والهندسية</p>
            <h3>الميزات:</h3>
            <ul style="text-align: right; display: inline-block;">
                <li>إدارة المشاريع</li>
                <li>الرسم ثنائي الأبعاد</li>
                <li>حساب الكميات</li>
                <li>التصور ثلاثي الأبعاد</li>
                <li>تصدير التقارير</li>
            </ul>
            <p>تم التطوير بواسطة فريق متخصص</p>
            <a href="/" style="color: white; font-size: 1.2em;">العودة للرئيسية</a>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام...")
    print("📱 افتح المتصفح على: http://localhost:5000")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    app.run(host='127.0.0.1', port=5000, debug=False)
