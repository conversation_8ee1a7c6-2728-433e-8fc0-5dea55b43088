# -*- coding: utf-8 -*-
"""
User Model
نموذج المستخدم
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from app import db

class User(UserMixin, db.Model):
    """
    User model for authentication and authorization
    نموذج المستخدم للمصادقة والتخويل
    """
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.Enum('Engineer', 'Supervisor', 'Client', name='user_roles'), 
                     nullable=False, default='Engineer')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    projects = db.relationship('Project', backref='owner', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, name, email, password, role='Engineer'):
        """Initialize user with hashed password"""
        self.name = name
        self.email = email
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    def get_role_display(self):
        """Get Arabic role display name"""
        role_names = {
            'Engineer': 'مهندس',
            'Supervisor': 'مشرف',
            'Client': 'عميل'
        }
        return role_names.get(self.role, self.role)
    
    def can_edit_project(self, project):
        """Check if user can edit a specific project"""
        return (self.role in ['Engineer', 'Supervisor'] and 
                (project.user_id == self.id or self.role == 'Supervisor'))
    
    def can_view_project(self, project):
        """Check if user can view a specific project"""
        return (project.user_id == self.id or 
                self.role == 'Supervisor' or 
                project.is_public)
    
    def __repr__(self):
        return f'<User {self.email}>'
    
    def to_dict(self):
        """Convert user to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'role': self.role,
            'role_display': self.get_role_display(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
