/**
 * 2D Drawing Canvas with Fabric.js
 * واجهة الرسم ثنائي الأبعاد باستخدام Fabric.js
 */

class DrawingCanvas {
    constructor(canvasId) {
        this.canvas = new fabric.Canvas(canvasId);
        this.currentTool = 'select';
        this.isDrawing = false;
        this.startX = 0;
        this.startY = 0;
        this.gridSize = 20;
        this.snapToGrid = true;
        
        this.initializeCanvas();
        this.setupEventListeners();
        this.createGrid();
    }
    
    initializeCanvas() {
        // Set canvas size
        this.canvas.setWidth(800);
        this.canvas.setHeight(600);
        
        // Canvas settings
        this.canvas.selection = true;
        this.canvas.preserveObjectStacking = true;
        
        // Default styles
        this.defaultStyles = {
            wall: {
                stroke: '#2c5aa0',
                strokeWidth: 8,
                fill: 'transparent',
                selectable: true
            },
            door: {
                stroke: '#8B4513',
                strokeWidth: 4,
                fill: '#DEB887',
                selectable: true
            },
            window: {
                stroke: '#4169E1',
                strokeWidth: 4,
                fill: '#87CEEB',
                selectable: true
            },
            room: {
                stroke: '#228B22',
                strokeWidth: 2,
                fill: 'rgba(144, 238, 144, 0.3)',
                selectable: true
            }
        };
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.on('mouse:down', (e) => this.onMouseDown(e));
        this.canvas.on('mouse:move', (e) => this.onMouseMove(e));
        this.canvas.on('mouse:up', (e) => this.onMouseUp(e));
        
        // Object events
        this.canvas.on('object:moving', (e) => this.onObjectMoving(e));
        this.canvas.on('object:scaling', (e) => this.onObjectScaling(e));
        this.canvas.on('object:rotating', (e) => this.onObjectRotating(e));
    }
    
    createGrid() {
        const gridSize = this.gridSize;
        const canvasWidth = this.canvas.getWidth();
        const canvasHeight = this.canvas.getHeight();
        
        // Create grid lines
        const gridLines = [];
        
        // Vertical lines
        for (let i = 0; i <= canvasWidth; i += gridSize) {
            const line = new fabric.Line([i, 0, i, canvasHeight], {
                stroke: '#ddd',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                excludeFromExport: true
            });
            gridLines.push(line);
        }
        
        // Horizontal lines
        for (let i = 0; i <= canvasHeight; i += gridSize) {
            const line = new fabric.Line([0, i, canvasWidth, i], {
                stroke: '#ddd',
                strokeWidth: 1,
                selectable: false,
                evented: false,
                excludeFromExport: true
            });
            gridLines.push(line);
        }
        
        // Add grid to canvas
        gridLines.forEach(line => this.canvas.add(line));
        this.canvas.sendToBack(...gridLines);
    }
    
    snapToGridPoint(point) {
        if (!this.snapToGrid) return point;
        
        return {
            x: Math.round(point.x / this.gridSize) * this.gridSize,
            y: Math.round(point.y / this.gridSize) * this.gridSize
        };
    }
    
    onMouseDown(e) {
        if (this.currentTool === 'select') return;
        
        this.isDrawing = true;
        const pointer = this.canvas.getPointer(e.e);
        const snappedPoint = this.snapToGridPoint(pointer);
        
        this.startX = snappedPoint.x;
        this.startY = snappedPoint.y;
        
        this.canvas.selection = false;
    }
    
    onMouseMove(e) {
        if (!this.isDrawing) return;
        
        const pointer = this.canvas.getPointer(e.e);
        const snappedPoint = this.snapToGridPoint(pointer);
        
        // Remove temporary object if exists
        if (this.tempObject) {
            this.canvas.remove(this.tempObject);
        }
        
        // Create temporary object based on current tool
        this.tempObject = this.createShape(
            this.startX, this.startY,
            snappedPoint.x, snappedPoint.y,
            this.currentTool
        );
        
        if (this.tempObject) {
            this.tempObject.set({
                opacity: 0.7,
                selectable: false
            });
            this.canvas.add(this.tempObject);
            this.canvas.renderAll();
        }
    }
    
    onMouseUp(e) {
        if (!this.isDrawing) return;
        
        this.isDrawing = false;
        this.canvas.selection = true;
        
        // Remove temporary object
        if (this.tempObject) {
            this.canvas.remove(this.tempObject);
        }
        
        const pointer = this.canvas.getPointer(e.e);
        const snappedPoint = this.snapToGridPoint(pointer);
        
        // Create final object
        const finalObject = this.createShape(
            this.startX, this.startY,
            snappedPoint.x, snappedPoint.y,
            this.currentTool
        );
        
        if (finalObject) {
            finalObject.set({
                opacity: 1,
                selectable: true
            });
            this.canvas.add(finalObject);
            this.canvas.setActiveObject(finalObject);
            this.canvas.renderAll();
        }
        
        this.tempObject = null;
    }
    
    createShape(x1, y1, x2, y2, tool) {
        const styles = this.defaultStyles[tool] || this.defaultStyles.wall;
        
        switch (tool) {
            case 'wall':
                return new fabric.Line([x1, y1, x2, y2], {
                    ...styles,
                    objectType: 'wall'
                });
                
            case 'door':
                const doorWidth = Math.abs(x2 - x1);
                const doorHeight = Math.abs(y2 - y1);
                return new fabric.Rect({
                    left: Math.min(x1, x2),
                    top: Math.min(y1, y2),
                    width: doorWidth || 60,
                    height: doorHeight || 20,
                    ...styles,
                    objectType: 'door'
                });
                
            case 'window':
                const windowWidth = Math.abs(x2 - x1);
                const windowHeight = Math.abs(y2 - y1);
                return new fabric.Rect({
                    left: Math.min(x1, x2),
                    top: Math.min(y1, y2),
                    width: windowWidth || 80,
                    height: windowHeight || 15,
                    ...styles,
                    objectType: 'window'
                });
                
            case 'room':
                const roomWidth = Math.abs(x2 - x1);
                const roomHeight = Math.abs(y2 - y1);
                if (roomWidth < 10 || roomHeight < 10) return null;
                
                return new fabric.Rect({
                    left: Math.min(x1, x2),
                    top: Math.min(y1, y2),
                    width: roomWidth,
                    height: roomHeight,
                    ...styles,
                    objectType: 'room'
                });
                
            default:
                return null;
        }
    }
    
    onObjectMoving(e) {
        if (this.snapToGrid) {
            const obj = e.target;
            const snappedPoint = this.snapToGridPoint({
                x: obj.left,
                y: obj.top
            });
            obj.set({
                left: snappedPoint.x,
                top: snappedPoint.y
            });
        }
    }
    
    onObjectScaling(e) {
        // Maintain aspect ratio for certain objects
        const obj = e.target;
        if (obj.objectType === 'door' || obj.objectType === 'window') {
            // Keep doors and windows rectangular
        }
    }
    
    onObjectRotating(e) {
        // Snap rotation to 15-degree increments
        const obj = e.target;
        const angle = obj.angle;
        const snappedAngle = Math.round(angle / 15) * 15;
        obj.set('angle', snappedAngle);
    }
    
    // Tool methods
    setTool(tool) {
        this.currentTool = tool;
        this.canvas.defaultCursor = tool === 'select' ? 'default' : 'crosshair';
        
        // Update UI
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
    }
    
    // Canvas operations
    clearCanvas() {
        const objects = this.canvas.getObjects().filter(obj => !obj.excludeFromExport);
        objects.forEach(obj => this.canvas.remove(obj));
        this.canvas.renderAll();
    }
    
    deleteSelected() {
        const activeObjects = this.canvas.getActiveObjects();
        activeObjects.forEach(obj => {
            if (!obj.excludeFromExport) {
                this.canvas.remove(obj);
            }
        });
        this.canvas.discardActiveObject();
        this.canvas.renderAll();
    }
    
    zoomIn() {
        const zoom = this.canvas.getZoom();
        this.canvas.setZoom(Math.min(zoom * 1.2, 3));
    }
    
    zoomOut() {
        const zoom = this.canvas.getZoom();
        this.canvas.setZoom(Math.max(zoom / 1.2, 0.3));
    }
    
    resetZoom() {
        this.canvas.setZoom(1);
        this.canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
        this.canvas.renderAll();
    }
    
    toggleGrid() {
        this.snapToGrid = !this.snapToGrid;
        
        // Toggle grid visibility
        const gridObjects = this.canvas.getObjects().filter(obj => obj.excludeFromExport);
        gridObjects.forEach(obj => {
            obj.set('visible', this.snapToGrid);
        });
        this.canvas.renderAll();
    }
    
    // Data operations
    saveDrawing() {
        const objects = this.canvas.getObjects().filter(obj => !obj.excludeFromExport);
        return JSON.stringify({
            version: '1.0',
            objects: objects.map(obj => obj.toObject(['objectType'])),
            canvasWidth: this.canvas.getWidth(),
            canvasHeight: this.canvas.getHeight()
        });
    }
    
    loadDrawing(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            // Clear existing objects (except grid)
            this.clearCanvas();
            
            // Load objects
            data.objects.forEach(objData => {
                fabric.util.enlivenObjects([objData], (objects) => {
                    objects.forEach(obj => {
                        this.canvas.add(obj);
                    });
                    this.canvas.renderAll();
                });
            });
            
        } catch (error) {
            console.error('Error loading drawing:', error);
            alert('خطأ في تحميل الرسم');
        }
    }
    
    exportAsImage() {
        // Hide grid for export
        const gridObjects = this.canvas.getObjects().filter(obj => obj.excludeFromExport);
        gridObjects.forEach(obj => obj.set('visible', false));
        
        const dataURL = this.canvas.toDataURL({
            format: 'png',
            quality: 1.0,
            multiplier: 2
        });
        
        // Show grid again
        gridObjects.forEach(obj => obj.set('visible', this.snapToGrid));
        this.canvas.renderAll();
        
        return dataURL;
    }
}
