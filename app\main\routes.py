# -*- coding: utf-8 -*-
"""
Main Routes
المسارات الرئيسية
"""

from flask import render_template, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.main import bp
from app.models.user import User
from app.models.project import Project
from app.models.drawing import Drawing
from app.models.calculation import Calculation
from sqlalchemy import func

@bp.route('/')
@bp.route('/index')
def index():
    """Home page - الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('main/index.html', title='الصفحة الرئيسية')

@bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard - لوحة التحكم الرئيسية"""
    # Get user's projects
    if current_user.role == 'Supervisor':
        # Supervisors can see all projects
        user_projects = Project.query.all()
    else:
        # Engineers and Clients see only their projects
        user_projects = current_user.projects.all()
    
    # Calculate statistics
    total_projects = len(user_projects)
    
    # Project status counts
    status_counts = {}
    for project in user_projects:
        status = project.status
        status_counts[status] = status_counts.get(status, 0) + 1
    
    # Recent projects (last 5)
    recent_projects = sorted(user_projects, key=lambda p: p.updated_at or p.created_at, reverse=True)[:5]
    
    # Total drawings and calculations
    total_drawings = 0
    total_calculations = 0
    
    for project in user_projects:
        total_drawings += project.drawings.count()
        total_calculations += project.calculations.count()
    
    # Project type distribution
    type_counts = {}
    for project in user_projects:
        project_type = project.project_type
        type_counts[project_type] = type_counts.get(project_type, 0) + 1
    
    # Calculate total area and budget
    total_area = sum(float(p.area) for p in user_projects if p.area)
    total_budget = sum(float(p.budget) for p in user_projects if p.budget)
    
    # Get recent activity (last 10 drawings and calculations)
    recent_drawings = []
    recent_calculations = []
    
    if current_user.role == 'Supervisor':
        recent_drawings = Drawing.query.order_by(Drawing.created_at.desc()).limit(5).all()
        recent_calculations = Calculation.query.order_by(Calculation.created_at.desc()).limit(5).all()
    else:
        project_ids = [p.id for p in user_projects]
        if project_ids:
            recent_drawings = Drawing.query.filter(Drawing.project_id.in_(project_ids))\
                                         .order_by(Drawing.created_at.desc()).limit(5).all()
            recent_calculations = Calculation.query.filter(Calculation.project_id.in_(project_ids))\
                                                 .order_by(Calculation.created_at.desc()).limit(5).all()
    
    dashboard_data = {
        'total_projects': total_projects,
        'total_drawings': total_drawings,
        'total_calculations': total_calculations,
        'total_area': total_area,
        'total_budget': total_budget,
        'status_counts': status_counts,
        'type_counts': type_counts,
        'recent_projects': recent_projects,
        'recent_drawings': recent_drawings,
        'recent_calculations': recent_calculations
    }
    
    return render_template('main/dashboard.html', title='لوحة التحكم', data=dashboard_data)

@bp.route('/about')
def about():
    """About page - صفحة حول النظام"""
    return render_template('main/about.html', title='حول النظام')

@bp.route('/help')
@login_required
def help():
    """Help page - صفحة المساعدة"""
    return render_template('main/help.html', title='المساعدة')
