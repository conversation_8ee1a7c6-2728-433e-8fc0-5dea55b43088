{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-8">
        <h1>تصدير المشاريع والتقارير</h1>
        <p class="text-muted">تصدير المشاريع بصيغ مختلفة وإنشاء التقارير المفصلة</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-primary" onclick="loadProjectData()">تحميل بيانات المشروع</button>
    </div>
</div>

<!-- Export Options -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header text-center">
                <h5>📄 تقرير PDF شامل</h5>
            </div>
            <div class="card-body text-center">
                <p>تقرير مفصل يحتوي على معلومات المشروع والرسوم والحسابات</p>
                <ul class="text-right small">
                    <li>معلومات المشروع</li>
                    <li>المخططات المعمارية</li>
                    <li>حسابات الكميات</li>
                    <li>تقديرات التكلفة</li>
                    <li>الجداول والإحصائيات</li>
                </ul>
                <button class="btn btn-danger btn-lg" onclick="exportPDF()" style="width: 100%;">
                    تصدير PDF
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header text-center">
                <h5>📐 ملف AutoCAD DXF</h5>
            </div>
            <div class="card-body text-center">
                <p>تصدير الرسوم بصيغة DXF للاستخدام في برامج CAD</p>
                <ul class="text-right small">
                    <li>متوافق مع AutoCAD</li>
                    <li>يحتفظ بالطبقات</li>
                    <li>دقة عالية</li>
                    <li>قابل للتعديل</li>
                    <li>مقياس صحيح</li>
                </ul>
                <button class="btn btn-info btn-lg" onclick="exportDXF()" style="width: 100%;">
                    تصدير DXF
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header text-center">
                <h5>💾 بيانات JSON</h5>
            </div>
            <div class="card-body text-center">
                <p>تصدير جميع بيانات المشروع بصيغة JSON للنسخ الاحتياطي</p>
                <ul class="text-right small">
                    <li>جميع بيانات المشروع</li>
                    <li>الرسوم والحسابات</li>
                    <li>قابل للاستيراد</li>
                    <li>نسخ احتياطي</li>
                    <li>تبادل البيانات</li>
                </ul>
                <button class="btn btn-success btn-lg" onclick="exportJSON()" style="width: 100%;">
                    تصدير JSON
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Export Settings -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>إعدادات التصدير</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اسم المشروع</label>
                            <input type="text" id="export-project-name" class="form-control" value="مشروع تجريبي">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اسم الملف</label>
                            <input type="text" id="export-filename" class="form-control" value="project-export">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">تضمين الرسوم</label>
                            <select id="include-drawings" class="form-control">
                                <option value="true">نعم</option>
                                <option value="false">لا</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">تضمين الحسابات</label>
                            <select id="include-calculations" class="form-control">
                                <option value="true">نعم</option>
                                <option value="false">لا</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Preview -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>معاينة التصدير</h6>
            </div>
            <div class="card-body">
                <div id="export-preview">
                    <p class="text-muted">اختر نوع التصدير لعرض المعاينة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export History -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>سجل التصديرات</h6>
            </div>
            <div class="card-body">
                <div id="export-history">
                    <p class="text-muted">لا توجد تصديرات سابقة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="sr-only">جاري التصدير...</span>
                </div>
                <h5>جاري تصدير الملف</h5>
                <p id="export-status">يرجى الانتظار...</p>
                <div class="progress">
                    <div id="export-progress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- jsPDF Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<!-- Export Manager Script -->
<script src="{{ url_for('static', filename='js/export-manager.js') }}"></script>

<script>
// Initialize export manager
let exportManager;
let exportHistory = [];

document.addEventListener('DOMContentLoaded', function() {
    exportManager = new ExportManager();
    loadExportHistory();
    
    // Load sample project data
    loadSampleData();
});

function loadSampleData() {
    // Sample project data
    const projectData = {
        name: document.getElementById('export-project-name').value,
        location: 'الرياض، المملكة العربية السعودية',
        type: 'سكني',
        area: 200,
        floors: 1,
        description: 'مشروع سكني نموذجي'
    };
    
    // Sample drawing data
    const drawingData = {
        scale: '1:100',
        units: 'متر',
        objectCount: 8,
        objects: [
            { objectType: 'room', left: 100, top: 100, width: 400, height: 300, getScaledWidth: () => 400, getScaledHeight: () => 300 },
            { objectType: 'wall', x1: 100, y1: 100, x2: 500, y2: 100 },
            { objectType: 'wall', x1: 500, y1: 100, x2: 500, y2: 400 },
            { objectType: 'wall', x1: 500, y1: 400, x2: 100, y2: 400 },
            { objectType: 'wall', x1: 100, y1: 400, x2: 100, y2: 100 },
            { objectType: 'door', left: 250, top: 95, width: 80, height: 10, getScaledWidth: () => 80, getScaledHeight: () => 10 },
            { objectType: 'window', left: 495, top: 200, width: 10, height: 100, getScaledWidth: () => 10, getScaledHeight: () => 100 }
        ]
    };
    
    // Sample calculation data
    const calculationData = {
        quantities: {
            concrete: { name: 'خرسانة', quantity: '12.5', unit: 'متر مكعب', unitPrice: '350', totalCost: '4375' },
            steel: { name: 'حديد تسليح', quantity: '1500', unit: 'كيلوجرام', unitPrice: '4.5', totalCost: '6750' },
            brick: { name: 'طوب', quantity: '180', unit: 'متر مربع', unitPrice: '45', totalCost: '8100' },
            tiles: { name: 'بلاط', quantity: '200', unit: 'متر مربع', unitPrice: '80', totalCost: '16000' }
        },
        costs: {
            subtotal: 35225,
            vat: 5283.75,
            total: 40508.75
        }
    };
    
    exportManager.setProjectData(projectData);
    exportManager.setDrawingData(drawingData);
    exportManager.setCalculationData(calculationData);
    
    updatePreview();
}

function loadProjectData() {
    // Try to load from drawing canvas if available
    if (typeof drawingCanvas !== 'undefined') {
        const objects = drawingCanvas.canvas.getObjects().filter(obj => !obj.excludeFromExport);
        const drawingData = {
            scale: '1:100',
            units: 'متر',
            objectCount: objects.length,
            objects: objects,
            imageData: drawingCanvas.exportAsImage()
        };
        exportManager.setDrawingData(drawingData);
    }
    
    // Try to load from calculator if available
    if (typeof calculator !== 'undefined' && currentReport) {
        exportManager.setCalculationData(currentReport);
    }
    
    updatePreview();
    alert('تم تحميل بيانات المشروع بنجاح!');
}

function updatePreview() {
    const previewDiv = document.getElementById('export-preview');
    
    let html = '<div class="row">';
    html += '<div class="col-md-4">';
    html += '<h6>معلومات المشروع</h6>';
    if (exportManager.projectData) {
        html += `<p><strong>الاسم:</strong> ${exportManager.projectData.name}</p>`;
        html += `<p><strong>الموقع:</strong> ${exportManager.projectData.location || 'غير محدد'}</p>`;
        html += `<p><strong>المساحة:</strong> ${exportManager.projectData.area || 'غير محدد'} م²</p>`;
    } else {
        html += '<p class="text-muted">لا توجد بيانات مشروع</p>';
    }
    html += '</div>';
    
    html += '<div class="col-md-4">';
    html += '<h6>بيانات الرسم</h6>';
    if (exportManager.drawingData) {
        html += `<p><strong>المقياس:</strong> ${exportManager.drawingData.scale}</p>`;
        html += `<p><strong>العناصر:</strong> ${exportManager.drawingData.objectCount}</p>`;
        html += `<p><strong>الوحدة:</strong> ${exportManager.drawingData.units}</p>`;
    } else {
        html += '<p class="text-muted">لا توجد بيانات رسم</p>';
    }
    html += '</div>';
    
    html += '<div class="col-md-4">';
    html += '<h6>بيانات الحسابات</h6>';
    if (exportManager.calculationData && exportManager.calculationData.costs) {
        html += `<p><strong>التكلفة الإجمالية:</strong> ${exportManager.calculationData.costs.total?.toLocaleString('ar-SA')} ريال</p>`;
        html += `<p><strong>عدد المواد:</strong> ${Object.keys(exportManager.calculationData.quantities || {}).length}</p>`;
    } else {
        html += '<p class="text-muted">لا توجد بيانات حسابات</p>';
    }
    html += '</div>';
    html += '</div>';
    
    previewDiv.innerHTML = html;
}

async function exportPDF() {
    try {
        showExportModal('جاري إنشاء تقرير PDF...');
        updateProgress(25);
        
        // Update project name
        exportManager.projectData.name = document.getElementById('export-project-name').value;
        
        updateProgress(50);
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate processing
        
        updateProgress(75);
        const filename = document.getElementById('export-filename').value + '.pdf';
        
        updateProgress(90);
        await exportManager.downloadPDF(filename);
        
        updateProgress(100);
        addToHistory('PDF', filename);
        
        setTimeout(() => {
            hideExportModal();
            alert('تم تصدير ملف PDF بنجاح!');
        }, 500);
        
    } catch (error) {
        hideExportModal();
        alert('خطأ في تصدير PDF: ' + error.message);
    }
}

async function exportDXF() {
    try {
        showExportModal('جاري إنشاء ملف DXF...');
        updateProgress(25);
        
        if (!exportManager.drawingData || !exportManager.drawingData.objects) {
            throw new Error('لا توجد بيانات رسم للتصدير');
        }
        
        updateProgress(50);
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate processing
        
        updateProgress(75);
        const filename = document.getElementById('export-filename').value + '.dxf';
        
        updateProgress(90);
        exportManager.downloadDXF(filename);
        
        updateProgress(100);
        addToHistory('DXF', filename);
        
        setTimeout(() => {
            hideExportModal();
            alert('تم تصدير ملف DXF بنجاح!');
        }, 500);
        
    } catch (error) {
        hideExportModal();
        alert('خطأ في تصدير DXF: ' + error.message);
    }
}

async function exportJSON() {
    try {
        showExportModal('جاري إنشاء ملف JSON...');
        updateProgress(50);
        
        const filename = document.getElementById('export-filename').value + '.json';
        
        updateProgress(75);
        exportManager.downloadJSON(filename);
        
        updateProgress(100);
        addToHistory('JSON', filename);
        
        setTimeout(() => {
            hideExportModal();
            alert('تم تصدير ملف JSON بنجاح!');
        }, 500);
        
    } catch (error) {
        hideExportModal();
        alert('خطأ في تصدير JSON: ' + error.message);
    }
}

function showExportModal(message) {
    document.getElementById('export-status').textContent = message;
    document.getElementById('export-progress').style.width = '0%';
    $('#exportModal').modal('show');
}

function hideExportModal() {
    $('#exportModal').modal('hide');
}

function updateProgress(percent) {
    document.getElementById('export-progress').style.width = percent + '%';
}

function addToHistory(type, filename) {
    const historyItem = {
        type: type,
        filename: filename,
        date: new Date().toLocaleString('ar-SA'),
        size: Math.floor(Math.random() * 1000) + 100 + ' KB' // Simulated size
    };
    
    exportHistory.unshift(historyItem);
    if (exportHistory.length > 10) {
        exportHistory = exportHistory.slice(0, 10);
    }
    
    saveExportHistory();
    displayExportHistory();
}

function displayExportHistory() {
    const historyDiv = document.getElementById('export-history');
    
    if (exportHistory.length === 0) {
        historyDiv.innerHTML = '<p class="text-muted">لا توجد تصديرات سابقة</p>';
        return;
    }
    
    let html = '<div class="table-responsive">';
    html += '<table class="table table-sm">';
    html += '<thead><tr><th>النوع</th><th>اسم الملف</th><th>التاريخ</th><th>الحجم</th></tr></thead>';
    html += '<tbody>';
    
    exportHistory.forEach(item => {
        html += '<tr>';
        html += `<td><span class="badge badge-${item.type === 'PDF' ? 'danger' : item.type === 'DXF' ? 'info' : 'success'}">${item.type}</span></td>`;
        html += `<td>${item.filename}</td>`;
        html += `<td>${item.date}</td>`;
        html += `<td>${item.size}</td>`;
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    historyDiv.innerHTML = html;
}

function loadExportHistory() {
    const saved = localStorage.getItem('export_history');
    if (saved) {
        exportHistory = JSON.parse(saved);
        displayExportHistory();
    }
}

function saveExportHistory() {
    localStorage.setItem('export_history', JSON.stringify(exportHistory));
}
</script>
{% endblock %}
