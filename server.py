#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import webbrowser
import threading
import time
import os

# HTML content
HTML_CONTENT = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 90%;
            animation: fadeIn 1s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        h1 {
            color: #2c5aa0;
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            color: #666;
            font-size: 1.3rem;
            margin-bottom: 2rem;
        }
        
        .success-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.2rem;
            display: inline-block;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 2rem 1rem;
            border-radius: 15px;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            color: #2c5aa0;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            margin: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(44, 90, 160, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid #90caf9;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .account {
            background: rgba(255,255,255,0.7);
            padding: 0.5rem 1rem;
            margin: 0.3rem 0;
            border-radius: 8px;
            border-left: 4px solid #2c5aa0;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #dee2e6;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="status">
        ✅ متصل
    </div>
    
    <div class="container">
        <h1>🏗️ نظام إدارة المشاريع المعمارية</h1>
        <p class="subtitle">نظام شامل لإدارة المشاريع المعمارية والهندسية</p>
        
        <div class="success-badge">
            ✅ النظام يعمل بنجاح!
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🏗️</div>
                <h3>إدارة المشاريع</h3>
                <p>إنشاء وإدارة المشاريع المعمارية بسهولة</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📐</div>
                <h3>الرسم ثنائي الأبعاد</h3>
                <p>أدوات رسم متقدمة وتفاعلية</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>حساب الكميات</h3>
                <p>حساب تلقائي للتكاليف والمواد</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <h3>التصور ثلاثي الأبعاد</h3>
                <p>نماذج تفاعلية ثلاثية الأبعاد</p>
            </div>
        </div>
        
        <div>
            <button class="btn" onclick="showLogin()">تسجيل الدخول</button>
            <button class="btn" onclick="showProjects()">المشاريع</button>
            <button class="btn" onclick="showDashboard()">لوحة التحكم</button>
        </div>
        
        <div class="info-box">
            <h4>🔑 الحسابات التجريبية:</h4>
            <div class="account">
                <strong>مدير النظام:</strong> <EMAIL> / admin123
            </div>
            <div class="account">
                <strong>مهندس:</strong> <EMAIL> / engineer123
            </div>
            <div class="account">
                <strong>عميل:</strong> <EMAIL> / client123
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 نظام إدارة المشاريع المعمارية - جميع الحقوق محفوظة</p>
            <p>تم التطوير بواسطة فريق متخصص</p>
        </div>
    </div>
    
    <script>
        function showLogin() {
            alert('صفحة تسجيل الدخول\\n\\nيمكنك استخدام الحسابات التجريبية المعروضة أعلاه');
        }
        
        function showProjects() {
            alert('صفحة المشاريع\\n\\n- مشروع سكني نموذجي (الرياض)\\n- مجمع تجاري (جدة)\\n- فيلا خاصة (الدمام)');
        }
        
        function showDashboard() {
            alert('لوحة التحكم\\n\\n📊 إجمالي المشاريع: 3\\n📐 الرسوم: 5\\n💰 التكلفة الإجمالية: 500,000 ريال');
        }
        
        // تأثير الترحيب
        setTimeout(function() {
            alert('🎉 مرحباً بك في نظام إدارة المشاريع المعمارية!\\n\\nالنظام يعمل بنجاح ويمكنك الآن استخدام جميع الميزات.');
        }, 1000);
    </script>
</body>
</html>
"""

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(HTML_CONTENT.encode('utf-8'))

def open_browser():
    time.sleep(2)
    webbrowser.open('http://localhost:8000')

def main():
    PORT = 8000
    
    print("=" * 60)
    print("🏗️  نظام إدارة المشاريع المعمارية")
    print("   Architectural Project Management System")
    print("=" * 60)
    print(f"🚀 بدء تشغيل الخادم على المنفذ {PORT}...")
    print(f"📱 افتح المتصفح على: http://localhost:{PORT}")
    print("⏹️  اضغط Ctrl+C للإيقاف")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل الخادم
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"✅ الخادم يعمل على http://localhost:{PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بنجاح!")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
