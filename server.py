#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import webbrowser
import threading
import time
import os

# HTML content
LOGIN_PAGE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🏗️ تسجيل الدخول</h1>
            <p>أدخل بياناتك للوصول إلى النظام</p>
        </div>

        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn">دخول</button>
        </form>

        <div class="back-link">
            <a href="/">← العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // محاكاة تسجيل الدخول
            if (username && password) {
                alert('تم تسجيل الدخول بنجاح! 🎉');
                window.location.href = '/dashboard';
            } else {
                alert('يرجى إدخال جميع البيانات المطلوبة');
            }
        }
    </script>
</body>
</html>
"""

HTML_CONTENT = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 90%;
            animation: fadeIn 1s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        h1 {
            color: #2c5aa0;
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            color: #666;
            font-size: 1.3rem;
            margin-bottom: 2rem;
        }
        
        .success-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.2rem;
            display: inline-block;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 2rem 1rem;
            border-radius: 15px;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .feature:active {
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature h3 {
            color: #2c5aa0;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            margin: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(44, 90, 160, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 2px solid #90caf9;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .account {
            background: rgba(255,255,255,0.7);
            padding: 0.5rem 1rem;
            margin: 0.3rem 0;
            border-radius: 8px;
            border-left: 4px solid #2c5aa0;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #dee2e6;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="status">
        ✅ متصل
    </div>
    
    <div class="container">
        <h1>🏗️ نظام إدارة المشاريع المعمارية</h1>
        <p class="subtitle">نظام شامل لإدارة المشاريع المعمارية والهندسية</p>
        
        <div class="success-badge">
            ✅ النظام يعمل بنجاح!
        </div>
        
        <div class="features">
            <div class="feature" onclick="manageProjects()" style="cursor: pointer;">
                <div class="feature-icon">🏗️</div>
                <h3>إدارة المشاريع</h3>
                <p>إنشاء وإدارة المشاريع المعمارية بسهولة</p>
            </div>
            <div class="feature" onclick="drawDimensions()" style="cursor: pointer;">
                <div class="feature-icon">📐</div>
                <h3>الرسم ثنائي الأبعاد</h3>
                <p>أدوات رسم متقدمة وتفاعلية</p>
            </div>
            <div class="feature" onclick="calculateQuantities()" style="cursor: pointer;">
                <div class="feature-icon">📊</div>
                <h3>حساب الكميات</h3>
                <p>حساب تلقائي للتكاليف والمواد</p>
            </div>
            <div class="feature" onclick="visualizeDimensions()" style="cursor: pointer;">
                <div class="feature-icon">🎯</div>
                <h3>التصور ثلاثي الأبعاد</h3>
                <p>نماذج تفاعلية ثلاثية الأبعاد</p>
            </div>
        </div>
        
        <div>
            <a href="/login" class="btn">تسجيل الدخول</a>
            <a href="/projects" class="btn">المشاريع</a>
            <a href="/dashboard" class="btn">لوحة التحكم</a>
        </div>
        
        <div class="info-box">
            <h4>🔑 الحسابات التجريبية:</h4>
            <div class="account">
                <strong>مدير النظام:</strong> <EMAIL> / admin123
            </div>
            <div class="account">
                <strong>مهندس:</strong> <EMAIL> / engineer123
            </div>
            <div class="account">
                <strong>عميل:</strong> <EMAIL> / client123
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 نظام إدارة المشاريع المعمارية - جميع الحقوق محفوظة</p>
            <p>تم التطوير بواسطة فريق متخصص</p>
        </div>
    </div>
    
    <script>
        function calculateQuantities() {
            alert('حاسبة الكميات - قريباً! 🔢\\n\\nستتمكن من حساب الكميات والتكاليف تلقائياً');
        }

        function drawDimensions() {
            alert('أداة الرسم ثنائي الأبعاد - قريباً! 📐\\n\\nأدوات رسم متقدمة وتفاعلية');
        }

        function manageProjects() {
            window.location.href = '/projects';
        }

        function visualizeDimensions() {
            alert('التصور ثلاثي الأبعاد - قريباً! 🎯\\n\\nنماذج تفاعلية ثلاثية الأبعاد');
        }
        
        // تأثير الترحيب
        setTimeout(function() {
            alert('🎉 مرحباً بك في نظام إدارة المشاريع المعمارية!\\n\\nالنظام يعمل بنجاح ويمكنك الآن التنقل بين الصفحات!\\n\\n✅ تسجيل الدخول\\n✅ إدارة المشاريع\\n✅ لوحة التحكم');
        }, 1000);
    </script>
</body>
</html>
"""

PROJECTS_PAGE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المشاريع - نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .project-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .project-title {
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .project-details {
            color: #666;
            margin-bottom: 15px;
        }

        .project-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-planning {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #cce5ff;
            color: #004085;
        }

        .add-project-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ إدارة المشاريع</h1>
            <p>جميع المشاريع المعمارية والهندسية</p>
        </div>

        <button class="add-project-btn" onclick="addProject()">+ إضافة مشروع جديد</button>

        <div class="projects-grid" id="projectsGrid">
            <div class="project-card">
                <h3 class="project-title">مجمع سكني - الرياض</h3>
                <div class="project-details">
                    <p><strong>العميل:</strong> شركة التطوير العقاري</p>
                    <p><strong>المساحة:</strong> 5000 متر مربع</p>
                    <p><strong>تاريخ البدء:</strong> 2024-01-15</p>
                </div>
                <span class="project-status status-active">قيد التنفيذ</span>
            </div>

            <div class="project-card">
                <h3 class="project-title">مركز تجاري - جدة</h3>
                <div class="project-details">
                    <p><strong>العميل:</strong> مجموعة الاستثمار التجاري</p>
                    <p><strong>المساحة:</strong> 8000 متر مربع</p>
                    <p><strong>تاريخ البدء:</strong> 2024-03-01</p>
                </div>
                <span class="project-status status-planning">مرحلة التخطيط</span>
            </div>

            <div class="project-card">
                <h3 class="project-title">فيلا خاصة - الدمام</h3>
                <div class="project-details">
                    <p><strong>العميل:</strong> عميل خاص</p>
                    <p><strong>المساحة:</strong> 800 متر مربع</p>
                    <p><strong>تاريخ البدء:</strong> 2023-10-01</p>
                </div>
                <span class="project-status status-completed">مكتمل</span>
            </div>
        </div>

        <div class="back-link">
            <a href="/">← العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        function addProject() {
            const projectName = prompt('اسم المشروع:');
            const clientName = prompt('اسم العميل:');
            const projectArea = prompt('مساحة المشروع (متر مربع):');

            if (projectName && clientName && projectArea) {
                const projectsGrid = document.getElementById('projectsGrid');
                const newProject = document.createElement('div');
                newProject.className = 'project-card';
                newProject.innerHTML = `
                    <h3 class="project-title">${projectName}</h3>
                    <div class="project-details">
                        <p><strong>العميل:</strong> ${clientName}</p>
                        <p><strong>المساحة:</strong> ${projectArea} متر مربع</p>
                        <p><strong>تاريخ البدء:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                    <span class="project-status status-planning">مرحلة التخطيط</span>
                `;
                projectsGrid.appendChild(newProject);
                alert('تم إضافة المشروع بنجاح! 🎉');
            }
        }
    </script>
</body>
</html>
"""

DASHBOARD_PAGE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المشاريع المعمارية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .recent-activity {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 لوحة التحكم</h1>
            <p>نظرة عامة على المشاريع والإحصائيات</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div>إجمالي المشاريع</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div>المشاريع النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div>المشاريع المكتملة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">2.5M</div>
                <div>إجمالي القيمة (ريال)</div>
            </div>
        </div>

        <div class="recent-activity">
            <h3>النشاطات الأخيرة</h3>
            <div class="activity-item">✅ تم الانتهاء من مرحلة التصميم - مجمع سكني الرياض</div>
            <div class="activity-item">📋 تم إضافة مشروع جديد - مركز تجاري جدة</div>
            <div class="activity-item">💰 تم استلام دفعة مالية - فيلا خاصة الدمام</div>
            <div class="activity-item">📊 تم تحديث تقرير المشروع - برج إداري الخبر</div>
        </div>

        <div class="back-link">
            <a href="/">← العودة للصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
"""

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(HTML_CONTENT.encode('utf-8'))
        elif self.path == '/login':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(LOGIN_PAGE.encode('utf-8'))
        elif self.path == '/projects':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(PROJECTS_PAGE.encode('utf-8'))
        elif self.path == '/dashboard':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(DASHBOARD_PAGE.encode('utf-8'))
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(b'404 - Page not found')

def open_browser():
    time.sleep(2)
    webbrowser.open('http://localhost:8001')

def main():
    PORT = 8001
    
    print("=" * 60)
    print("🏗️  نظام إدارة المشاريع المعمارية")
    print("   Architectural Project Management System")
    print("=" * 60)
    print(f"🚀 بدء تشغيل الخادم على المنفذ {PORT}...")
    print(f"📱 افتح المتصفح على: http://localhost:{PORT}")
    print("⏹️  اضغط Ctrl+C للإيقاف")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل الخادم
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"✅ الخادم يعمل على http://localhost:{PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم بنجاح!")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
