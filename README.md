# نظام إدارة المشاريع المعمارية
## Architectural Project Management System

نظام شامل لإدارة المشاريع المعمارية والهندسية مع إمكانيات الرسم ثنائي الأبعاد والتصور ثلاثي الأبعاد ودعم اللغة العربية.

## المميزات الرئيسية

### 🏗️ إدارة المشاريع
- إنشاء وإدارة المشاريع المعمارية
- تتبع حالة المشروع والتقدم
- إدارة المستخدمين والصلاحيات
- لوحة تحكم شاملة بالإحصائيات

### 📐 الرسم ثنائي الأبعاد
- واجهة رسم تفاعلية باستخدام Fabric.js
- أدوات رسم متقدمة (جدران، أبواب، نوافذ، غرف)
- نظام طبقات وشبكة للمحاذاة
- حفظ وتحميل الرسوم بصيغة JSON

### 📊 حساب الكميات
- حساب تلقائي للمساحات والأحجام
- تقدير كميات المواد والتكاليف
- تقارير مفصلة بالأسعار
- دعم العملة السعودية

### 🎯 التصور ثلاثي الأبعاد
- تحويل الرسوم ثنائية الأبعاد إلى نماذج ثلاثية الأبعاد
- عارض تفاعلي باستخدام Three.js
- مناظير متعددة (علوي، أمامي، جانبي، متساوي القياس)
- تحكم في الإضاءة والظلال والمواد

### 📄 التصدير والتقارير
- تصدير تقارير PDF شاملة
- تصدير ملفات DXF متوافقة مع AutoCAD
- تصدير بيانات JSON للنسخ الاحتياطي
- طباعة التقارير والمخططات

### 🌐 دعم اللغة العربية
- واجهة مستخدم كاملة باللغة العربية
- دعم النص من اليمين إلى اليسار (RTL)
- خطوط عربية متقدمة
- تنسيق التواريخ والأرقام العربية

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- Flask 2.2+
- SQLite (مدمج مع Python)
- متصفح ويب حديث يدعم HTML5

### المكتبات المطلوبة
```
Flask==2.2.5
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.2
WTForms==3.0.1
Werkzeug==2.2.3
SQLAlchemy==1.4.48
Jinja2==3.1.2
reportlab==4.0.4
ezdxf==1.0.3
Pillow==10.0.1
python-dotenv==1.0.0
bcrypt==4.0.1
email-validator==2.0.0
MarkupSafe==2.1.3
```

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd architectural-project-management
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
# في Windows
venv\Scripts\activate
# في Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python setup.py
```

### 5. تشغيل التطبيق
```bash
python simple_app.py
```

### 6. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## الحسابات التجريبية

### مدير النظام
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`
- الصلاحيات: عرض جميع المشاريع والإشراف

### مهندس
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `engineer123`
- الصلاحيات: إنشاء وتعديل المشاريع والرسوم

### عميل
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `client123`
- الصلاحيات: عرض المشاريع المخصصة له فقط

## دليل الاستخدام

### إنشاء مشروع جديد
1. سجل الدخول بحساب مهندس أو مشرف
2. انقر على "المشاريع" في القائمة الرئيسية
3. انقر على "إنشاء مشروع جديد"
4. املأ بيانات المشروع واحفظ

### الرسم ثنائي الأبعاد
1. انتقل إلى صفحة "الرسم"
2. اختر الأداة المناسبة (جدار، باب، نافذة، غرفة)
3. ارسم على اللوحة بالنقر والسحب
4. استخدم أدوات التحكم للتكبير والتصغير
5. احفظ الرسم عند الانتهاء

### حساب الكميات
1. انتقل إلى صفحة "الحسابات"
2. أدخل بيانات المشروع أو احسب من الرسم
3. راجع النتائج والتقرير المفصل
4. احفظ الحساب أو صدره

### التصور ثلاثي الأبعاد
1. انتقل إلى صفحة "التصور ثلاثي الأبعاد"
2. انقر على "تحميل من الرسم"
3. استخدم الماوس للتنقل في المشهد
4. غير وضع العرض والإعدادات حسب الحاجة

### التصدير
1. انتقل إلى صفحة "التصدير"
2. اختر نوع التصدير المطلوب (PDF، DXF، JSON)
3. عدل الإعدادات حسب الحاجة
4. انقر على زر التصدير المناسب

## الهيكل التقني

### قاعدة البيانات
- **users**: معلومات المستخدمين والصلاحيات
- **projects**: بيانات المشاريع
- **drawings**: الرسوم وبياناتها
- **calculations**: الحسابات والتقديرات

### الملفات الرئيسية
- `simple_app.py`: التطبيق الرئيسي المبسط
- `app.py`: التطبيق الكامل (قيد التطوير)
- `models.py`: نماذج قاعدة البيانات
- `setup.py`: سكريبت الإعداد الأولي

### الملفات الثابتة
- `app/static/css/arabic-rtl.css`: أنماط CSS للعربية
- `app/static/js/drawing-canvas.js`: محرك الرسم ثنائي الأبعاد
- `app/static/js/quantity-calculator.js`: محرك حساب الكميات
- `app/static/js/3d-viewer.js`: محرك التصور ثلاثي الأبعاد
- `app/static/js/export-manager.js`: مدير التصدير

## الأمان والحماية

### المصادقة
- تشفير كلمات المرور باستخدام bcrypt
- جلسات آمنة مع Flask-Login
- التحقق من صحة البيانات

### الصلاحيات
- نظام أدوار متعدد المستويات
- تحكم في الوصول للمشاريع
- حماية المسارات الحساسة

## الاستكشاف وإصلاح الأخطاء

### مشاكل شائعة
1. **خطأ في تشغيل التطبيق**: تأكد من تثبيت جميع المتطلبات
2. **مشاكل في قاعدة البيانات**: شغل `python setup.py` مرة أخرى
3. **مشاكل في الرسم**: تأكد من تحميل مكتبة Fabric.js
4. **مشاكل في التصور ثلاثي الأبعاد**: تأكد من تحميل مكتبة Three.js

### سجلات الأخطاء
- تحقق من وحدة التحكم في المتصفح للأخطاء JavaScript
- راجع سجلات Flask في الطرفية
- تأكد من صحة مسارات الملفات

## التطوير المستقبلي

### الميزات المخططة
- [ ] تكامل مع قواعد بيانات خارجية
- [ ] API للتطبيقات الخارجية
- [ ] تطبيق جوال
- [ ] تحليلات متقدمة
- [ ] تعاون متعدد المستخدمين في الوقت الفعلي

### التحسينات التقنية
- [ ] تحسين الأداء
- [ ] دعم ملفات أكبر
- [ ] نسخ احتياطي تلقائي
- [ ] تشفير البيانات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات والاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راسل فريق التطوير
- راجع الوثائق التقنية

---

© 2025 نظام إدارة المشاريع المعمارية. جميع الحقوق محفوظة.
