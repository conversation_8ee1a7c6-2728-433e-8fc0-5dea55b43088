/**
 * 3D Visualization Engine with Three.js
 * محرك التصور ثلاثي الأبعاد باستخدام Three.js
 */

class ThreeDViewer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.lights = [];
        this.meshes = [];
        this.floorHeight = 3; // ارتفاع الطابق بالمتر
        this.scale = 0.05; // مقياس التحويل من البكسل إلى المتر
        
        this.materials = {
            wall: new THREE.MeshLambertMaterial({ color: 0x8B7355 }),
            floor: new THREE.MeshLambertMaterial({ color: 0xF5F5DC }),
            ceiling: new THREE.MeshLambertMaterial({ color: 0xFFFFFF }),
            door: new THREE.MeshLambertMaterial({ color: 0x8B4513 }),
            window: new THREE.MeshLambertMaterial({ color: 0x87CEEB, transparent: true, opacity: 0.7 }),
            room: new THREE.MeshLambertMaterial({ color: 0x90EE90, transparent: true, opacity: 0.3 })
        };
        
        this.initializeScene();
        this.setupLighting();
        this.setupControls();
        this.animate();
    }
    
    initializeScene() {
        // إنشاء المشهد
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);
        
        // إنشاء الكاميرا
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(10, 10, 10);
        this.camera.lookAt(0, 0, 0);
        
        // إنشاء المُرندر
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(width, height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);
        
        // إضافة شبكة مرجعية
        const gridHelper = new THREE.GridHelper(20, 20);
        this.scene.add(gridHelper);
        
        // إضافة محاور الإحداثيات
        const axesHelper = new THREE.AxesHelper(5);
        this.scene.add(axesHelper);
    }
    
    setupLighting() {
        // إضاءة محيطية
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        this.lights.push(ambientLight);
        
        // إضاءة اتجاهية (الشمس)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        this.lights.push(directionalLight);
        
        // إضاءة نقطية
        const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
        pointLight.position.set(0, 8, 0);
        this.scene.add(pointLight);
        this.lights.push(pointLight);
    }
    
    setupControls() {
        // التحكم في الكاميرا (يتطلب OrbitControls)
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.25;
            this.controls.enableZoom = true;
        }
        
        // التعامل مع تغيير حجم النافذة
        window.addEventListener('resize', () => this.onWindowResize(), false);
    }
    
    onWindowResize() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        this.renderer.render(this.scene, this.camera);
    }
    
    // تحويل الرسم ثنائي الأبعاد إلى نموذج ثلاثي الأبعاد
    convertFrom2D(canvasObjects) {
        this.clearScene();
        
        const walls = [];
        const rooms = [];
        const doors = [];
        const windows = [];
        
        // تصنيف العناصر
        canvasObjects.forEach(obj => {
            if (obj.objectType === 'wall') {
                walls.push(obj);
            } else if (obj.objectType === 'room') {
                rooms.push(obj);
            } else if (obj.objectType === 'door') {
                doors.push(obj);
            } else if (obj.objectType === 'window') {
                windows.push(obj);
            }
        });
        
        // إنشاء الغرف (الأرضيات والأسقف)
        rooms.forEach(room => this.createRoom(room));
        
        // إنشاء الجدران
        walls.forEach(wall => this.createWall(wall));
        
        // إنشاء الأبواب
        doors.forEach(door => this.createDoor(door));
        
        // إنشاء النوافذ
        windows.forEach(window => this.createWindow(window));
        
        // تحديث المشهد
        this.fitCameraToScene();
    }
    
    createRoom(roomObj) {
        const width = roomObj.getScaledWidth() * this.scale;
        const height = roomObj.getScaledHeight() * this.scale;
        const x = (roomObj.left + roomObj.getScaledWidth() / 2) * this.scale;
        const z = (roomObj.top + roomObj.getScaledHeight() / 2) * this.scale;
        
        // إنشاء الأرضية
        const floorGeometry = new THREE.PlaneGeometry(width, height);
        const floor = new THREE.Mesh(floorGeometry, this.materials.floor);
        floor.rotation.x = -Math.PI / 2;
        floor.position.set(x, 0, z);
        floor.receiveShadow = true;
        this.scene.add(floor);
        this.meshes.push(floor);
        
        // إنشاء السقف
        const ceilingGeometry = new THREE.PlaneGeometry(width, height);
        const ceiling = new THREE.Mesh(ceilingGeometry, this.materials.ceiling);
        ceiling.rotation.x = Math.PI / 2;
        ceiling.position.set(x, this.floorHeight, z);
        this.scene.add(ceiling);
        this.meshes.push(ceiling);
    }
    
    createWall(wallObj) {
        const x1 = wallObj.x1 * this.scale;
        const z1 = wallObj.y1 * this.scale;
        const x2 = wallObj.x2 * this.scale;
        const z2 = wallObj.y2 * this.scale;
        
        const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(z2 - z1, 2));
        const angle = Math.atan2(z2 - z1, x2 - x1);
        
        const wallGeometry = new THREE.BoxGeometry(length, this.floorHeight, 0.2);
        const wall = new THREE.Mesh(wallGeometry, this.materials.wall);
        
        wall.position.set(
            (x1 + x2) / 2,
            this.floorHeight / 2,
            (z1 + z2) / 2
        );
        wall.rotation.y = angle;
        wall.castShadow = true;
        wall.receiveShadow = true;
        
        this.scene.add(wall);
        this.meshes.push(wall);
    }
    
    createDoor(doorObj) {
        const width = doorObj.getScaledWidth() * this.scale;
        const height = doorObj.getScaledHeight() * this.scale;
        const x = (doorObj.left + doorObj.getScaledWidth() / 2) * this.scale;
        const z = (doorObj.top + doorObj.getScaledHeight() / 2) * this.scale;
        
        const doorGeometry = new THREE.BoxGeometry(width, 2, height);
        const door = new THREE.Mesh(doorGeometry, this.materials.door);
        door.position.set(x, 1, z);
        door.castShadow = true;
        
        this.scene.add(door);
        this.meshes.push(door);
    }
    
    createWindow(windowObj) {
        const width = windowObj.getScaledWidth() * this.scale;
        const height = windowObj.getScaledHeight() * this.scale;
        const x = (windowObj.left + windowObj.getScaledWidth() / 2) * this.scale;
        const z = (windowObj.top + windowObj.getScaledHeight() / 2) * this.scale;
        
        const windowGeometry = new THREE.BoxGeometry(width, 1.5, height);
        const window = new THREE.Mesh(windowGeometry, this.materials.window);
        window.position.set(x, 1.5, z);
        
        this.scene.add(window);
        this.meshes.push(window);
    }
    
    // مسح المشهد
    clearScene() {
        this.meshes.forEach(mesh => {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) mesh.material.dispose();
        });
        this.meshes = [];
    }
    
    // تعديل الكاميرا لتناسب المشهد
    fitCameraToScene() {
        const box = new THREE.Box3();
        
        this.meshes.forEach(mesh => {
            box.expandByObject(mesh);
        });
        
        if (!box.isEmpty()) {
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            
            const maxDim = Math.max(size.x, size.y, size.z);
            const fov = this.camera.fov * (Math.PI / 180);
            let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
            
            cameraZ *= 2; // إضافة مسافة إضافية
            
            this.camera.position.set(center.x + cameraZ, center.y + cameraZ, center.z + cameraZ);
            this.camera.lookAt(center);
            
            if (this.controls) {
                this.controls.target.copy(center);
                this.controls.update();
            }
        }
    }
    
    // تغيير وضع العرض
    setViewMode(mode) {
        const center = new THREE.Vector3(0, 0, 0);
        const distance = 15;
        
        switch (mode) {
            case 'top':
                this.camera.position.set(0, distance, 0);
                this.camera.lookAt(center);
                break;
            case 'front':
                this.camera.position.set(0, 5, distance);
                this.camera.lookAt(center);
                break;
            case 'side':
                this.camera.position.set(distance, 5, 0);
                this.camera.lookAt(center);
                break;
            case 'isometric':
                this.camera.position.set(distance, distance, distance);
                this.camera.lookAt(center);
                break;
        }
        
        if (this.controls) {
            this.controls.target.copy(center);
            this.controls.update();
        }
    }
    
    // تغيير مواد العرض
    setMaterial(objectType, color, opacity = 1) {
        if (this.materials[objectType]) {
            this.materials[objectType].color.setHex(color);
            this.materials[objectType].opacity = opacity;
            this.materials[objectType].transparent = opacity < 1;
        }
    }
    
    // تصدير المشهد كصورة
    exportAsImage() {
        return this.renderer.domElement.toDataURL('image/png');
    }
    
    // تبديل الإضاءة
    toggleLighting() {
        this.lights.forEach(light => {
            light.visible = !light.visible;
        });
    }
    
    // تبديل الظلال
    toggleShadows() {
        this.renderer.shadowMap.enabled = !this.renderer.shadowMap.enabled;
        this.meshes.forEach(mesh => {
            mesh.castShadow = this.renderer.shadowMap.enabled;
            mesh.receiveShadow = this.renderer.shadowMap.enabled;
        });
    }
    
    // إضافة نص ثلاثي الأبعاد
    addText(text, position, size = 1) {
        if (typeof THREE.TextGeometry !== 'undefined') {
            const loader = new THREE.FontLoader();
            loader.load('path/to/font.json', (font) => {
                const textGeometry = new THREE.TextGeometry(text, {
                    font: font,
                    size: size,
                    height: 0.1
                });
                
                const textMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
                const textMesh = new THREE.Mesh(textGeometry, textMaterial);
                textMesh.position.copy(position);
                
                this.scene.add(textMesh);
                this.meshes.push(textMesh);
            });
        }
    }
    
    // الحصول على معلومات المشهد
    getSceneInfo() {
        return {
            meshCount: this.meshes.length,
            triangles: this.renderer.info.render.triangles,
            vertices: this.renderer.info.render.vertices,
            calls: this.renderer.info.render.calls
        };
    }
}
