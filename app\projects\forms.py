# -*- coding: utf-8 -*-
"""
Project Forms
نماذج المشاريع
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DecimalField, IntegerField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional

class ProjectForm(FlaskForm):
    """Project creation/edit form - نموذج إنشاء/تعديل المشروع"""
    name = StringField('اسم المشروع', validators=[
        DataRequired(message='اسم المشروع مطلوب'),
        Length(min=2, max=200, message='اسم المشروع يجب أن يكون بين 2 و 200 حرف')
    ])
    
    location = StringField('موقع المشروع', validators=[
        Optional(),
        Length(max=300, message='موقع المشروع يجب ألا يتجاوز 300 حرف')
    ])
    
    description = TextAreaField('وصف المشروع', validators=[
        Optional(),
        Length(max=1000, message='وصف المشروع يجب ألا يتجاوز 1000 حرف')
    ])
    
    project_type = SelectField('نوع المشروع', choices=[
        ('Residential', 'سكني'),
        ('Commercial', 'تجاري'),
        ('Industrial', 'صناعي'),
        ('Mixed', 'مختلط')
    ], default='Residential', validators=[DataRequired(message='نوع المشروع مطلوب')])
    
    status = SelectField('حالة المشروع', choices=[
        ('Planning', 'تخطيط'),
        ('Design', 'تصميم'),
        ('Construction', 'إنشاء'),
        ('Completed', 'مكتمل'),
        ('On Hold', 'معلق')
    ], default='Planning', validators=[DataRequired(message='حالة المشروع مطلوبة')])
    
    budget = DecimalField('الميزانية (ريال سعودي)', validators=[
        Optional(),
        NumberRange(min=0, message='الميزانية يجب أن تكون رقماً موجباً')
    ], places=2)
    
    area = DecimalField('المساحة الإجمالية (متر مربع)', validators=[
        Optional(),
        NumberRange(min=0, message='المساحة يجب أن تكون رقماً موجباً')
    ], places=2)
    
    floors = IntegerField('عدد الطوابق', validators=[
        Optional(),
        NumberRange(min=1, max=100, message='عدد الطوابق يجب أن يكون بين 1 و 100')
    ], default=1)
    
    is_public = BooleanField('مشروع عام (يمكن للعملاء رؤيته)')
    
    submit = SubmitField('حفظ المشروع')

class ProjectSearchForm(FlaskForm):
    """Project search form - نموذج البحث في المشاريع"""
    search = StringField('البحث في المشاريع', validators=[
        Optional(),
        Length(max=100, message='نص البحث يجب ألا يتجاوز 100 حرف')
    ])
    
    project_type = SelectField('نوع المشروع', choices=[
        ('', 'جميع الأنواع'),
        ('Residential', 'سكني'),
        ('Commercial', 'تجاري'),
        ('Industrial', 'صناعي'),
        ('Mixed', 'مختلط')
    ], default='')
    
    status = SelectField('حالة المشروع', choices=[
        ('', 'جميع الحالات'),
        ('Planning', 'تخطيط'),
        ('Design', 'تصميم'),
        ('Construction', 'إنشاء'),
        ('Completed', 'مكتمل'),
        ('On Hold', 'معلق')
    ], default='')
    
    submit = SubmitField('بحث')

class ProjectDeleteForm(FlaskForm):
    """Project deletion confirmation form - نموذج تأكيد حذف المشروع"""
    confirm = BooleanField('أؤكد رغبتي في حذف هذا المشروع نهائياً', validators=[
        DataRequired(message='يجب تأكيد الحذف')
    ])
    
    submit = SubmitField('حذف المشروع نهائياً')
