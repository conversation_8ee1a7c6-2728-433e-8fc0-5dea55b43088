/* Arabic RTL Styles - أنماط العربية من اليمين إلى اليسار */

/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Root variables for Arabic theme */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --arabic-font: 'Noto Sans Arabic', 'Cairo', Arial, sans-serif;
    --english-font: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Base RTL setup */
html {
    direction: rtl;
    text-align: right;
}

body {
    font-family: var(--arabic-font);
    direction: rtl;
    text-align: right;
    background-color: var(--secondary-color);
    color: var(--dark-color);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--arabic-font);
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.8;
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border: none;
    border-radius: 0.375rem;
    font-family: var(--arabic-font);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1e3d72;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e2e6ea;
}

.btn-success {
    background-color: var(--accent-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem;
    font-family: var(--arabic-font);
    font-size: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: white;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    direction: rtl;
    text-align: right;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--danger-color);
}

/* Select dropdowns */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
    padding-left: 2.25rem;
}

/* Cards */
.card {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px var(--shadow-color);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
}

/* Navigation */
.navbar {
    background-color: var(--primary-color);
    padding: 1rem 0;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
}

.navbar-nav {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 0.5rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: white;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--primary-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Utility classes */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.float-left { float: left !important; }
.float-right { float: right !important; }

.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 1rem !important; }
.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 1rem !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

/* Container and grid */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col {
    flex: 1;
    padding: 0 0.75rem;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
    .col-md-6 { flex: 0 0 100%; max-width: 100%; }
    .col-md-4 { flex: 0 0 100%; max-width: 100%; }
    .col-md-3 { flex: 0 0 100%; max-width: 100%; }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}
