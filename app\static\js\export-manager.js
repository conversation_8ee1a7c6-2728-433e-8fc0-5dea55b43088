/**
 * Export Manager for PDF and DXF files
 * مدير التصدير لملفات PDF و DXF
 */

class ExportManager {
    constructor() {
        this.projectData = null;
        this.drawingData = null;
        this.calculationData = null;
    }
    
    // تعيين بيانات المشروع
    setProjectData(data) {
        this.projectData = data;
    }
    
    // تعيين بيانات الرسم
    setDrawingData(data) {
        this.drawingData = data;
    }
    
    // تعيين بيانات الحسابات
    setCalculationData(data) {
        this.calculationData = data;
    }
    
    // تصدير تقرير PDF شامل
    async exportComprehensiveReport() {
        if (!this.projectData) {
            throw new Error('بيانات المشروع غير متوفرة');
        }
        
        // إنشاء محتوى التقرير
        const reportContent = this.generateReportContent();
        
        // إنشاء PDF باستخدام jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // إعداد الخط العربي
        doc.setFont('helvetica');
        doc.setFontSize(16);
        
        // إضافة العنوان
        doc.text('تقرير المشروع المعماري', 105, 20, { align: 'center' });
        doc.setFontSize(12);
        doc.text(this.projectData.name || 'مشروع غير محدد', 105, 30, { align: 'center' });
        
        let yPosition = 50;
        
        // معلومات المشروع
        yPosition = this.addProjectInfo(doc, yPosition);
        
        // إضافة الرسم إذا كان متوفراً
        if (this.drawingData) {
            yPosition = this.addDrawingToReport(doc, yPosition);
        }
        
        // إضافة الحسابات إذا كانت متوفرة
        if (this.calculationData) {
            yPosition = this.addCalculationsToReport(doc, yPosition);
        }
        
        // إضافة التوقيع والتاريخ
        this.addFooter(doc);
        
        return doc;
    }
    
    // إضافة معلومات المشروع إلى PDF
    addProjectInfo(doc, startY) {
        let y = startY;
        
        doc.setFontSize(14);
        doc.text('معلومات المشروع', 20, y);
        y += 10;
        
        doc.setFontSize(10);
        const projectInfo = [
            `اسم المشروع: ${this.projectData.name || 'غير محدد'}`,
            `الموقع: ${this.projectData.location || 'غير محدد'}`,
            `النوع: ${this.projectData.type || 'غير محدد'}`,
            `المساحة: ${this.projectData.area || 'غير محدد'} متر مربع`,
            `عدد الطوابق: ${this.projectData.floors || 1}`,
            `تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}`
        ];
        
        projectInfo.forEach(info => {
            doc.text(info, 20, y);
            y += 7;
        });
        
        return y + 10;
    }
    
    // إضافة الرسم إلى التقرير
    addDrawingToReport(doc, startY) {
        let y = startY;
        
        doc.setFontSize(14);
        doc.text('المخطط المعماري', 20, y);
        y += 10;
        
        // إضافة صورة الرسم إذا كانت متوفرة
        if (this.drawingData.imageData) {
            try {
                doc.addImage(this.drawingData.imageData, 'PNG', 20, y, 170, 120);
                y += 130;
            } catch (error) {
                doc.text('خطأ في تحميل صورة الرسم', 20, y);
                y += 10;
            }
        }
        
        // إضافة معلومات الرسم
        doc.setFontSize(10);
        const drawingInfo = [
            `المقياس: ${this.drawingData.scale || '1:100'}`,
            `الوحدة: ${this.drawingData.units || 'متر'}`,
            `عدد العناصر: ${this.drawingData.objectCount || 0}`,
            `تاريخ آخر تعديل: ${new Date().toLocaleDateString('ar-SA')}`
        ];
        
        drawingInfo.forEach(info => {
            doc.text(info, 20, y);
            y += 7;
        });
        
        return y + 10;
    }
    
    // إضافة الحسابات إلى التقرير
    addCalculationsToReport(doc, startY) {
        let y = startY;
        
        doc.setFontSize(14);
        doc.text('حسابات الكميات والتكاليف', 20, y);
        y += 10;
        
        if (this.calculationData.quantities) {
            doc.setFontSize(10);
            
            // جدول الكميات
            const tableData = [];
            Object.entries(this.calculationData.quantities).forEach(([material, data]) => {
                tableData.push([
                    data.name || material,
                    data.quantity || '0',
                    data.unit || '',
                    data.unitPrice || '0',
                    data.totalCost || '0'
                ]);
            });
            
            // رؤوس الجدول
            const headers = ['المادة', 'الكمية', 'الوحدة', 'سعر الوحدة', 'التكلفة الإجمالية'];
            
            // رسم الجدول
            y = this.drawTable(doc, headers, tableData, 20, y);
            
            // إضافة المجاميع
            y += 10;
            doc.setFontSize(12);
            if (this.calculationData.costs) {
                doc.text(`المجموع الفرعي: ${this.calculationData.costs.subtotal?.toLocaleString('ar-SA') || '0'} ريال`, 20, y);
                y += 7;
                doc.text(`ضريبة القيمة المضافة: ${this.calculationData.costs.vat?.toLocaleString('ar-SA') || '0'} ريال`, 20, y);
                y += 7;
                doc.setFont('helvetica', 'bold');
                doc.text(`المجموع الإجمالي: ${this.calculationData.costs.total?.toLocaleString('ar-SA') || '0'} ريال`, 20, y);
            }
        }
        
        return y + 10;
    }
    
    // رسم جدول في PDF
    drawTable(doc, headers, data, x, y) {
        const cellWidth = 30;
        const cellHeight = 7;
        let currentY = y;
        
        // رسم رؤوس الجدول
        doc.setFont('helvetica', 'bold');
        headers.forEach((header, index) => {
            doc.rect(x + (index * cellWidth), currentY, cellWidth, cellHeight);
            doc.text(header, x + (index * cellWidth) + 2, currentY + 5);
        });
        currentY += cellHeight;
        
        // رسم بيانات الجدول
        doc.setFont('helvetica', 'normal');
        data.forEach(row => {
            row.forEach((cell, index) => {
                doc.rect(x + (index * cellWidth), currentY, cellWidth, cellHeight);
                doc.text(String(cell), x + (index * cellWidth) + 2, currentY + 5);
            });
            currentY += cellHeight;
        });
        
        return currentY;
    }
    
    // إضافة التذييل
    addFooter(doc) {
        const pageHeight = doc.internal.pageSize.height;
        doc.setFontSize(8);
        doc.text('تم إنشاء هذا التقرير بواسطة نظام إدارة المشاريع المعمارية', 105, pageHeight - 20, { align: 'center' });
        doc.text(`تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}`, 105, pageHeight - 15, { align: 'center' });
    }
    
    // تصدير ملف DXF
    exportDXF() {
        if (!this.drawingData || !this.drawingData.objects) {
            throw new Error('بيانات الرسم غير متوفرة');
        }
        
        // إنشاء محتوى DXF
        let dxfContent = this.generateDXFHeader();
        dxfContent += this.generateDXFTables();
        dxfContent += this.generateDXFEntities();
        dxfContent += this.generateDXFFooter();
        
        return dxfContent;
    }
    
    // إنشاء رأس ملف DXF
    generateDXFHeader() {
        return `0
SECTION
2
HEADER
9
$ACADVER
1
AC1015
9
$HANDSEED
5
FFFF
0
ENDSEC
`;
    }
    
    // إنشاء جداول DXF
    generateDXFTables() {
        return `0
SECTION
2
TABLES
0
TABLE
2
LAYER
5
2
330
0
100
AcDbSymbolTable
70
1
0
LAYER
5
10
330
2
100
AcDbSymbolTableRecord
100
AcDbLayerTableRecord
2
0
70
0
62
7
6
CONTINUOUS
0
ENDTAB
0
ENDSEC
`;
    }
    
    // إنشاء كيانات DXF
    generateDXFEntities() {
        let entities = `0
SECTION
2
ENTITIES
`;
        
        if (this.drawingData.objects) {
            this.drawingData.objects.forEach((obj, index) => {
                entities += this.convertObjectToDXF(obj, index);
            });
        }
        
        entities += `0
ENDSEC
`;
        
        return entities;
    }
    
    // تحويل عنصر إلى DXF
    convertObjectToDXF(obj, index) {
        const scale = 0.05; // مقياس التحويل
        
        switch (obj.objectType) {
            case 'wall':
                return this.createDXFLine(obj, index, scale);
            case 'room':
                return this.createDXFRectangle(obj, index, scale);
            case 'door':
            case 'window':
                return this.createDXFRectangle(obj, index, scale);
            default:
                return '';
        }
    }
    
    // إنشاء خط في DXF
    createDXFLine(obj, index, scale) {
        const x1 = (obj.x1 || 0) * scale;
        const y1 = (obj.y1 || 0) * scale;
        const x2 = (obj.x2 || 0) * scale;
        const y2 = (obj.y2 || 0) * scale;
        
        return `0
LINE
5
${(100 + index).toString(16).toUpperCase()}
330
1F
100
AcDbEntity
8
0
100
AcDbLine
10
${x1.toFixed(6)}
20
${y1.toFixed(6)}
30
0.0
11
${x2.toFixed(6)}
21
${y2.toFixed(6)}
31
0.0
`;
    }
    
    // إنشاء مستطيل في DXF
    createDXFRectangle(obj, index, scale) {
        const x = (obj.left || 0) * scale;
        const y = (obj.top || 0) * scale;
        const width = (obj.getScaledWidth ? obj.getScaledWidth() : obj.width || 0) * scale;
        const height = (obj.getScaledHeight ? obj.getScaledHeight() : obj.height || 0) * scale;
        
        let dxf = '';
        
        // الخطوط الأربعة للمستطيل
        const lines = [
            { x1: x, y1: y, x2: x + width, y2: y },
            { x1: x + width, y1: y, x2: x + width, y2: y + height },
            { x1: x + width, y1: y + height, x2: x, y2: y + height },
            { x1: x, y1: y + height, x2: x, y2: y }
        ];
        
        lines.forEach((line, lineIndex) => {
            dxf += `0
LINE
5
${(100 + index * 4 + lineIndex).toString(16).toUpperCase()}
330
1F
100
AcDbEntity
8
0
100
AcDbLine
10
${line.x1.toFixed(6)}
20
${line.y1.toFixed(6)}
30
0.0
11
${line.x2.toFixed(6)}
21
${line.y2.toFixed(6)}
31
0.0
`;
        });
        
        return dxf;
    }
    
    // إنشاء تذييل DXF
    generateDXFFooter() {
        return `0
EOF
`;
    }
    
    // تحميل ملف
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // تصدير PDF
    async downloadPDF(filename = 'project-report.pdf') {
        try {
            const doc = await this.exportComprehensiveReport();
            doc.save(filename);
            return true;
        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            throw error;
        }
    }
    
    // تصدير DXF
    downloadDXF(filename = 'drawing.dxf') {
        try {
            const dxfContent = this.exportDXF();
            this.downloadFile(dxfContent, filename, 'application/dxf');
            return true;
        } catch (error) {
            console.error('خطأ في تصدير DXF:', error);
            throw error;
        }
    }
    
    // تصدير بيانات JSON
    downloadJSON(filename = 'project-data.json') {
        const data = {
            project: this.projectData,
            drawing: this.drawingData,
            calculations: this.calculationData,
            exportDate: new Date().toISOString()
        };
        
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, filename, 'application/json');
    }
}
