# -*- coding: utf-8 -*-
"""
Database Models
نماذج قاعدة البيانات
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """User model for authentication and authorization"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.<PERSON>('Engineer', 'Supervisor', 'Client', name='user_roles'), 
                     nullable=False, default='Engineer')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    projects = db.relationship('Project', backref='owner', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, name, email, password, role='Engineer'):
        self.name = name
        self.email = email
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_role_display(self):
        role_names = {
            'Engineer': 'مهندس',
            'Supervisor': 'مشرف',
            'Client': 'عميل'
        }
        return role_names.get(self.role, self.role)
    
    def can_edit_project(self, project):
        return (self.role in ['Engineer', 'Supervisor'] and 
                (project.user_id == self.id or self.role == 'Supervisor'))
    
    def can_view_project(self, project):
        return (project.user_id == self.id or 
                self.role == 'Supervisor' or 
                project.is_public)
    
    def __repr__(self):
        return f'<User {self.email}>'

class Project(db.Model):
    """Project model for managing architectural projects"""
    __tablename__ = 'projects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    location = db.Column(db.String(300), nullable=True)
    description = db.Column(db.Text, nullable=True)
    project_type = db.Column(db.Enum('Residential', 'Commercial', 'Industrial', 'Mixed', 
                                   name='project_types'), default='Residential')
    status = db.Column(db.Enum('Planning', 'Design', 'Construction', 'Completed', 'On Hold',
                              name='project_status'), default='Planning')
    budget = db.Column(db.Numeric(15, 2), nullable=True)
    area = db.Column(db.Numeric(10, 2), nullable=True)
    floors = db.Column(db.Integer, default=1)
    is_public = db.Column(db.Boolean, default=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    drawings = db.relationship('Drawing', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    calculations = db.relationship('Calculation', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, name, user_id, location=None, description=None, project_type='Residential'):
        self.name = name
        self.user_id = user_id
        self.location = location
        self.description = description
        self.project_type = project_type
    
    def get_type_display(self):
        type_names = {
            'Residential': 'سكني',
            'Commercial': 'تجاري',
            'Industrial': 'صناعي',
            'Mixed': 'مختلط'
        }
        return type_names.get(self.project_type, self.project_type)
    
    def get_status_display(self):
        status_names = {
            'Planning': 'تخطيط',
            'Design': 'تصميم',
            'Construction': 'إنشاء',
            'Completed': 'مكتمل',
            'On Hold': 'معلق'
        }
        return status_names.get(self.status, self.status)
    
    def get_latest_drawing(self):
        return self.drawings.order_by(Drawing.created_at.desc()).first()
    
    def get_progress_percentage(self):
        progress_map = {
            'Planning': 10,
            'Design': 30,
            'Construction': 70,
            'Completed': 100,
            'On Hold': 0
        }
        return progress_map.get(self.status, 0)
    
    def __repr__(self):
        return f'<Project {self.name}>'

class Drawing(db.Model):
    """Drawing model for storing 2D floor plans"""
    __tablename__ = 'drawings'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    drawing_data_json = db.Column(db.Text, nullable=False)
    version = db.Column(db.Integer, default=1, nullable=False)
    floor_level = db.Column(db.Integer, default=0)
    scale = db.Column(db.String(20), default='1:100')
    units = db.Column(db.Enum('meters', 'feet', 'centimeters', name='drawing_units'), default='meters')
    is_active = db.Column(db.Boolean, default=True)
    thumbnail_path = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, project_id, name, drawing_data_json, description=None, floor_level=0):
        self.project_id = project_id
        self.name = name
        self.drawing_data_json = drawing_data_json
        self.description = description
        self.floor_level = floor_level
    
    def get_drawing_data(self):
        try:
            return json.loads(self.drawing_data_json)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def __repr__(self):
        return f'<Drawing {self.name} v{self.version}>'

class Calculation(db.Model):
    """Calculation model for storing quantity calculations"""
    __tablename__ = 'calculations'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False, index=True)
    calculation_type = db.Column(db.Enum('Area', 'Volume', 'Material', 'Cost', 'Custom', 
                                       name='calculation_types'), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    value = db.Column(db.Numeric(15, 4), nullable=False)
    unit = db.Column(db.String(50), nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=True)
    total_cost = db.Column(db.Numeric(15, 2), nullable=True)
    currency = db.Column(db.String(10), default='SAR')
    formula = db.Column(db.Text, nullable=True)
    parameters = db.Column(db.Text, nullable=True)
    drawing_id = db.Column(db.Integer, db.ForeignKey('drawings.id'), nullable=True, index=True)
    is_approved = db.Column(db.Boolean, default=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    drawing = db.relationship('Drawing', backref='calculations')
    approver = db.relationship('User', foreign_keys=[approved_by])
    
    def __init__(self, project_id, calculation_type, name, value, unit, description=None):
        self.project_id = project_id
        self.calculation_type = calculation_type
        self.name = name
        self.value = value
        self.unit = unit
        self.description = description
    
    def get_type_display(self):
        type_names = {
            'Area': 'مساحة',
            'Volume': 'حجم',
            'Material': 'مواد',
            'Cost': 'تكلفة',
            'Custom': 'مخصص'
        }
        return type_names.get(self.calculation_type, self.calculation_type)
    
    def __repr__(self):
        return f'<Calculation {self.name}: {self.value} {self.unit}>'
