# -*- coding: utf-8 -*-
"""
Calculation Model
نموذج الحسابات
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json
from app import db

class Calculation(db.Model):
    """
    Calculation model for storing quantity calculations and cost estimates
    نموذج الحسابات لحفظ حسابات الكميات وتقديرات التكلفة
    """
    __tablename__ = 'calculations'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False, index=True)
    calculation_type = db.Column(db.Enum('Area', 'Volume', 'Material', 'Cost', 'Custom', 
                                       name='calculation_types'), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    value = db.Column(db.Numeric(15, 4), nullable=False)
    unit = db.Column(db.String(50), nullable=False)  # m², m³, kg, pieces, etc.
    unit_price = db.Column(db.Numeric(10, 2), nullable=True)  # Price per unit
    total_cost = db.Column(db.Numeric(15, 2), nullable=True)  # Total cost
    currency = db.Column(db.String(10), default='SAR')  # Saudi Riyal
    formula = db.Column(db.Text, nullable=True)  # Calculation formula
    parameters = db.Column(db.Text, nullable=True)  # JSON parameters used in calculation
    drawing_id = db.Column(db.Integer, db.ForeignKey('drawings.id'), nullable=True, index=True)
    is_approved = db.Column(db.Boolean, default=False)
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    drawing = db.relationship('Drawing', backref='calculations')
    approver = db.relationship('User', foreign_keys=[approved_by])
    
    def __init__(self, project_id, calculation_type, name, value, unit, description=None):
        """Initialize calculation"""
        self.project_id = project_id
        self.calculation_type = calculation_type
        self.name = name
        self.value = value
        self.unit = unit
        self.description = description
    
    def get_type_display(self):
        """Get Arabic calculation type display name"""
        type_names = {
            'Area': 'مساحة',
            'Volume': 'حجم',
            'Material': 'مواد',
            'Cost': 'تكلفة',
            'Custom': 'مخصص'
        }
        return type_names.get(self.calculation_type, self.calculation_type)
    
    def get_unit_display(self):
        """Get Arabic unit display name"""
        unit_names = {
            'm²': 'متر مربع',
            'm³': 'متر مكعب',
            'm': 'متر',
            'kg': 'كيلوجرام',
            'ton': 'طن',
            'pieces': 'قطعة',
            'bags': 'كيس',
            'liters': 'لتر'
        }
        return unit_names.get(self.unit, self.unit)
    
    def get_parameters(self):
        """Parse and return parameters as Python object"""
        try:
            return json.loads(self.parameters) if self.parameters else {}
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_parameters(self, params):
        """Set parameters from Python object"""
        self.parameters = json.dumps(params, ensure_ascii=False)
    
    def calculate_total_cost(self):
        """Calculate total cost based on value and unit price"""
        if self.unit_price:
            self.total_cost = float(self.value) * float(self.unit_price)
        return self.total_cost
    
    def format_value(self):
        """Format value with appropriate decimal places"""
        if self.calculation_type in ['Area', 'Volume']:
            return f"{float(self.value):.2f}"
        elif self.calculation_type == 'Material':
            return f"{float(self.value):.1f}"
        else:
            return f"{float(self.value):.4f}"
    
    def format_cost(self):
        """Format cost with currency"""
        if self.total_cost:
            return f"{float(self.total_cost):,.2f} {self.currency}"
        return None
    
    def approve(self, user_id):
        """Approve this calculation"""
        self.is_approved = True
        self.approved_by = user_id
        self.updated_at = datetime.utcnow()
    
    def __repr__(self):
        return f'<Calculation {self.name}: {self.value} {self.unit}>'
    
    def to_dict(self):
        """Convert calculation to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'calculation_type': self.calculation_type,
            'calculation_type_display': self.get_type_display(),
            'name': self.name,
            'description': self.description,
            'value': float(self.value),
            'formatted_value': self.format_value(),
            'unit': self.unit,
            'unit_display': self.get_unit_display(),
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_cost': float(self.total_cost) if self.total_cost else None,
            'formatted_cost': self.format_cost(),
            'currency': self.currency,
            'formula': self.formula,
            'parameters': self.get_parameters(),
            'drawing_id': self.drawing_id,
            'is_approved': self.is_approved,
            'approved_by': self.approved_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
