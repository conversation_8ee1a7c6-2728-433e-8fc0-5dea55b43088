{% extends "base/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>المشاريع</h1>
        <p class="text-muted">إدارة وعرض جميع المشاريع المعمارية</p>
    </div>
    <div class="col-md-4 text-left">
        {% if current_user.role in ['Engineer', 'Supervisor'] %}
            <a href="{{ url_for('projects.create') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
        {% endif %}
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row">
            <div class="col-md-4">
                {{ search_form.search(class="form-control", placeholder="البحث في المشاريع...") }}
            </div>
            <div class="col-md-3">
                {{ search_form.project_type(class="form-control form-select") }}
            </div>
            <div class="col-md-3">
                {{ search_form.status(class="form-control form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.submit(class="btn btn-secondary", style="width: 100%;") }}
            </div>
        </form>
    </div>
</div>

<!-- Projects List -->
{% if projects.items %}
    <div class="row">
        {% for project in projects.items %}
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h5 style="margin: 0;">
                                <a href="{{ url_for('projects.view', id=project.id) }}">{{ project.name }}</a>
                            </h5>
                            <span class="badge" style="
                                background-color: 
                                {% if project.status == 'Completed' %}#28a745
                                {% elif project.status == 'Construction' %}#ffc107
                                {% elif project.status == 'Design' %}#17a2b8
                                {% elif project.status == 'Planning' %}#6c757d
                                {% else %}#dc3545{% endif %};
                                color: white;">
                                {{ project.get_status_display() }}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            <strong>الموقع:</strong> {{ project.location or 'لم يتم تحديده' }}
                        </p>
                        <p class="text-muted mb-2">
                            <strong>النوع:</strong> {{ project.get_type_display() }}
                        </p>
                        {% if project.description %}
                            <p class="mb-3">{{ project.description[:100] }}{% if project.description|length > 100 %}...{% endif %}</p>
                        {% endif %}
                        
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <small class="text-muted">المساحة</small>
                                <div><strong>{{ "{:,.0f}".format(project.area) if project.area else "غير محدد" }}</strong></div>
                                {% if project.area %}<small class="text-muted">م²</small>{% endif %}
                            </div>
                            <div class="col-4">
                                <small class="text-muted">الطوابق</small>
                                <div><strong>{{ project.floors }}</strong></div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">الرسوم</small>
                                <div><strong>{{ project.drawings.count() }}</strong></div>
                            </div>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mb-3">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <small class="text-muted">التقدم</small>
                                <small class="text-muted">{{ project.get_progress_percentage() }}%</small>
                            </div>
                            <div style="background-color: #f8f9fa; border-radius: 0.25rem; height: 0.5rem; overflow: hidden;">
                                <div style="background-color: var(--primary-color); height: 100%; width: {{ project.get_progress_percentage() }}%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <a href="{{ url_for('projects.view', id=project.id) }}" class="btn btn-primary btn-sm">عرض التفاصيل</a>
                            {% if current_user.can_edit_project(project) %}
                                <a href="{{ url_for('projects.edit', id=project.id) }}" class="btn btn-secondary btn-sm">تعديل</a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer text-muted text-center">
                        <small>
                            آخر تحديث: {{ project.updated_at.strftime('%Y/%m/%d %H:%M') if project.updated_at else project.created_at.strftime('%Y/%m/%d %H:%M') }}
                        </small>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if projects.pages > 1 %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if projects.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('projects.index', page=projects.prev_num, **request.args) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in projects.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != projects.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('projects.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if projects.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('projects.index', page=projects.next_num, **request.args) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
{% else %}
    <div class="text-center">
        <div class="card">
            <div class="card-body">
                <h3 class="text-muted">لا توجد مشاريع</h3>
                <p class="text-muted">لم يتم العثور على أي مشاريع تطابق معايير البحث.</p>
                {% if current_user.role in ['Engineer', 'Supervisor'] %}
                    <a href="{{ url_for('projects.create') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
