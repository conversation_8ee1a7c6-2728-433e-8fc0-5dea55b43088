# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع المعمارية
Architectural Project Management System
"""

from flask import Flask
from flask_login import LoginManager
import os
from dotenv import load_dotenv
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models import db, User, Project, Drawing, Calculation

# Load environment variables
load_dotenv()

# Initialize Flask extensions
login_manager = LoginManager()

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///architectural_pm.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['WTF_CSRF_ENABLED'] = False  # Disabled for simplicity
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file upload

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)

    # Login manager configuration
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    # User loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.projects import bp as projects_bp
    app.register_blueprint(projects_bp, url_prefix='/projects')

    from app.drawings import bp as drawings_bp
    app.register_blueprint(drawings_bp, url_prefix='/drawings')

    from app.calculations import bp as calculations_bp
    app.register_blueprint(calculations_bp, url_prefix='/calculations')

    from app.exports import bp as exports_bp
    app.register_blueprint(exports_bp, url_prefix='/exports')

    # Create database tables
    with app.app_context():
        db.create_all()

    return app
