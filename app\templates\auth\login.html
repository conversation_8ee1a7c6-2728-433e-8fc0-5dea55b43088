{% extends "base/base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3>تسجيل الدخول</h3>
                <p style="margin: 0; color: #6c757d;">أدخل بياناتك للوصول إلى حسابك</p>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="form-group">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            {% for error in form.email.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            {% for error in form.password.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <div style="display: flex; align-items: center;">
                            {{ form.remember_me(style="margin-left: 0.5rem;") }}
                            {{ form.remember_me.label() }}
                        </div>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg", style="width: 100%;") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p style="margin: 0;">
                    ليس لديك حساب؟ 
                    <a href="{{ url_for('auth.register') }}">إنشاء حساب جديد</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
