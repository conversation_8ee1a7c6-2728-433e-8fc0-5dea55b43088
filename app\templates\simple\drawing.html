{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-8">
        <h1>واجهة الرسم ثنائي الأبعاد</h1>
        <p class="text-muted">أدوات الرسم المعماري التفاعلي</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-success" onclick="saveDrawing()">حفظ الرسم</button>
        <button class="btn btn-info" onclick="exportImage()">تصدير صورة</button>
    </div>
</div>

<!-- Drawing Toolbar -->
<div class="card mb-3">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary tool-btn active" data-tool="select" onclick="setTool('select')">
                        ↖️ تحديد
                    </button>
                    <button type="button" class="btn btn-outline-primary tool-btn" data-tool="wall" onclick="setTool('wall')">
                        🧱 جدار
                    </button>
                    <button type="button" class="btn btn-outline-primary tool-btn" data-tool="door" onclick="setTool('door')">
                        🚪 باب
                    </button>
                    <button type="button" class="btn btn-outline-primary tool-btn" data-tool="window" onclick="setTool('window')">
                        🪟 نافذة
                    </button>
                    <button type="button" class="btn btn-outline-primary tool-btn" data-tool="room" onclick="setTool('room')">
                        🏠 غرفة
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-left">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="drawingCanvas.zoomIn()">
                        🔍+ تكبير
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="drawingCanvas.zoomOut()">
                        🔍- تصغير
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="drawingCanvas.resetZoom()">
                        🎯 إعادة تعيين
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="drawingCanvas.toggleGrid()">
                        ⚏ الشبكة
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="drawingCanvas.deleteSelected()">
                        🗑️ حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Drawing Canvas -->
<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-body text-center">
                <canvas id="drawing-canvas" style="border: 2px solid #ddd; border-radius: 8px;"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Properties Panel -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6>خصائص العنصر</h6>
            </div>
            <div class="card-body">
                <div id="object-properties">
                    <p class="text-muted">اختر عنصراً لعرض خصائصه</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>طبقات الرسم</h6>
            </div>
            <div class="card-body">
                <div id="layers-panel">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="layer-walls" checked>
                        <label class="form-check-label" for="layer-walls">الجدران</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="layer-doors" checked>
                        <label class="form-check-label" for="layer-doors">الأبواب</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="layer-windows" checked>
                        <label class="form-check-label" for="layer-windows">النوافذ</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="layer-rooms" checked>
                        <label class="form-check-label" for="layer-rooms">الغرف</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>معلومات الرسم</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <div>المقياس: 1:100</div>
                    <div>الوحدة: متر</div>
                    <div>حجم الشبكة: 20px</div>
                    <div id="canvas-info">العناصر: 0</div>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>تعليمات الاستخدام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>أدوات الرسم:</h6>
                        <ul class="small">
                            <li><strong>تحديد:</strong> لتحديد ونقل العناصر</li>
                            <li><strong>جدار:</strong> لرسم الجدران</li>
                            <li><strong>باب:</strong> لإضافة الأبواب</li>
                            <li><strong>نافذة:</strong> لإضافة النوافذ</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>التحكم:</h6>
                        <ul class="small">
                            <li><strong>النقر والسحب:</strong> لرسم العناصر</li>
                            <li><strong>النقر:</strong> لتحديد العناصر</li>
                            <li><strong>Delete:</strong> لحذف العنصر المحدد</li>
                            <li><strong>Ctrl+Z:</strong> للتراجع</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>الشبكة:</h6>
                        <ul class="small">
                            <li>تساعد في المحاذاة الدقيقة</li>
                            <li>يمكن إخفاؤها/إظهارها</li>
                            <li>حجم الشبكة: 20 بكسل</li>
                            <li>الالتصاق تلقائي</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>التصدير:</h6>
                        <ul class="small">
                            <li><strong>حفظ:</strong> حفظ الرسم كـ JSON</li>
                            <li><strong>صورة:</strong> تصدير كصورة PNG</li>
                            <li><strong>PDF:</strong> تصدير تقرير PDF</li>
                            <li><strong>DXF:</strong> تصدير لـ AutoCAD</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Fabric.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>

<!-- Drawing Canvas Script -->
<script src="{{ url_for('static', filename='js/drawing-canvas.js') }}"></script>

<script>
// Initialize drawing canvas
let drawingCanvas;

document.addEventListener('DOMContentLoaded', function() {
    drawingCanvas = new DrawingCanvas('drawing-canvas');
    
    // Update canvas info
    updateCanvasInfo();
    
    // Listen for canvas changes
    drawingCanvas.canvas.on('object:added', updateCanvasInfo);
    drawingCanvas.canvas.on('object:removed', updateCanvasInfo);
    drawingCanvas.canvas.on('selection:created', showObjectProperties);
    drawingCanvas.canvas.on('selection:updated', showObjectProperties);
    drawingCanvas.canvas.on('selection:cleared', hideObjectProperties);
});

function setTool(tool) {
    drawingCanvas.setTool(tool);
}

function updateCanvasInfo() {
    const objects = drawingCanvas.canvas.getObjects().filter(obj => !obj.excludeFromExport);
    document.getElementById('canvas-info').textContent = `العناصر: ${objects.length}`;
}

function showObjectProperties() {
    const activeObject = drawingCanvas.canvas.getActiveObject();
    if (!activeObject) return;
    
    const propertiesPanel = document.getElementById('object-properties');
    const objectType = activeObject.objectType || 'unknown';
    
    let html = `<h6>${getObjectTypeName(objectType)}</h6>`;
    html += `<div class="mb-2">`;
    html += `<label class="form-label">العرض:</label>`;
    html += `<input type="number" class="form-control form-control-sm" value="${Math.round(activeObject.getScaledWidth())}" onchange="updateObjectWidth(this.value)">`;
    html += `</div>`;
    html += `<div class="mb-2">`;
    html += `<label class="form-label">الارتفاع:</label>`;
    html += `<input type="number" class="form-control form-control-sm" value="${Math.round(activeObject.getScaledHeight())}" onchange="updateObjectHeight(this.value)">`;
    html += `</div>`;
    html += `<div class="mb-2">`;
    html += `<label class="form-label">الزاوية:</label>`;
    html += `<input type="number" class="form-control form-control-sm" value="${Math.round(activeObject.angle)}" onchange="updateObjectAngle(this.value)">`;
    html += `</div>`;
    
    propertiesPanel.innerHTML = html;
}

function hideObjectProperties() {
    document.getElementById('object-properties').innerHTML = '<p class="text-muted">اختر عنصراً لعرض خصائصه</p>';
}

function getObjectTypeName(type) {
    const names = {
        'wall': 'جدار',
        'door': 'باب',
        'window': 'نافذة',
        'room': 'غرفة'
    };
    return names[type] || 'عنصر';
}

function updateObjectWidth(value) {
    const activeObject = drawingCanvas.canvas.getActiveObject();
    if (activeObject) {
        activeObject.set('scaleX', value / activeObject.width);
        drawingCanvas.canvas.renderAll();
    }
}

function updateObjectHeight(value) {
    const activeObject = drawingCanvas.canvas.getActiveObject();
    if (activeObject) {
        activeObject.set('scaleY', value / activeObject.height);
        drawingCanvas.canvas.renderAll();
    }
}

function updateObjectAngle(value) {
    const activeObject = drawingCanvas.canvas.getActiveObject();
    if (activeObject) {
        activeObject.set('angle', value);
        drawingCanvas.canvas.renderAll();
    }
}

function saveDrawing() {
    const drawingData = drawingCanvas.saveDrawing();
    
    // Create download link
    const blob = new Blob([drawingData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'drawing.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    alert('تم حفظ الرسم بنجاح!');
}

function exportImage() {
    const dataURL = drawingCanvas.exportAsImage();
    
    // Create download link
    const a = document.createElement('a');
    a.href = dataURL;
    a.download = 'drawing.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    alert('تم تصدير الصورة بنجاح!');
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.key === 'Delete') {
        drawingCanvas.deleteSelected();
    } else if (e.ctrlKey && e.key === 'z') {
        e.preventDefault();
        // TODO: Implement undo functionality
    }
});
</script>
{% endblock %}
