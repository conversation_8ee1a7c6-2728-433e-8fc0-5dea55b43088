# -*- coding: utf-8 -*-
"""
Authentication Routes
مسارات المصادقة
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, current_user, login_required
from models import db, User
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm, ChangePasswordForm, ProfileForm

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login route - مسار تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        
        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('حسابك معطل. يرجى الاتصال بالمدير.', 'error')
                return redirect(url_for('auth.login'))
            
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.dashboard')
            
            flash(f'مرحباً {user.name}! تم تسجيل الدخول بنجاح.', 'success')
            return redirect(next_page)
        else:
            flash('البريد الإلكتروني أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('auth/login.html', title='تسجيل الدخول', form=form)

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """Registration route - مسار التسجيل"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            name=form.name.data,
            email=form.email.data,
            password=form.password.data,
            role=form.role.data
        )
        
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', title='إنشاء حساب جديد', form=form)

@bp.route('/logout')
@login_required
def logout():
    """Logout route - مسار تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile route - مسار الملف الشخصي"""
    form = ProfileForm(current_user.email)
    
    if form.validate_on_submit():
        current_user.name = form.name.data
        current_user.email = form.email.data
        db.session.commit()
        flash('تم تحديث ملفك الشخصي بنجاح.', 'success')
        return redirect(url_for('auth.profile'))
    elif request.method == 'GET':
        form.name.data = current_user.name
        form.email.data = current_user.email
    
    return render_template('auth/profile.html', title='الملف الشخصي', form=form)

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password route - مسار تغيير كلمة المرور"""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('تم تغيير كلمة المرور بنجاح.', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('كلمة المرور الحالية غير صحيحة.', 'error')
    
    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)
