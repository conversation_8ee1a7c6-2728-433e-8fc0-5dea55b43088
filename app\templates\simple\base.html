<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة المشاريع المعمارية{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/arabic-rtl.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="row" style="width: 100%; align-items: center;">
                <div class="col">
                    <a class="navbar-brand" href="{{ url_for('index') }}">
                        🏗️ نظام إدارة المشاريع المعمارية
                    </a>
                </div>
                <div class="col" style="flex: 0 0 auto;">
                    <ul class="navbar-nav" style="flex-direction: row;">
                        {% if current_user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('projects') }}">المشاريع</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('drawing') }}">الرسم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('calculations') }}">الحسابات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('viewer_3d') }}">التصور ثلاثي الأبعاد</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('exports') }}">التصدير</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">{{ current_user.name }}</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('login') }}">تسجيل الدخول</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('register') }}">إنشاء حساب</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="mt-5" style="background-color: var(--light-color); padding: 2rem 0; border-top: 1px solid var(--border-color);">
        <div class="container">
            <div class="row">
                <div class="col text-center">
                    <p style="margin: 0; color: var(--dark-color);">
                        © 2025 نظام إدارة المشاريع المعمارية. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
