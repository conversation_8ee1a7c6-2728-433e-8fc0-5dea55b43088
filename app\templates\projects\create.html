{% extends "base/base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3>إنشاء مشروع جديد</h3>
                <p style="margin: 0; color: #6c757d;">املأ البيانات التالية لإنشاء مشروع معماري جديد</p>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    {% for error in form.name.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.location.label(class="form-label") }}
                                {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                                {% if form.location.errors %}
                                    {% for error in form.location.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control", rows="4") }}
                        {% if form.description.errors %}
                            {% for error in form.description.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.project_type.label(class="form-label") }}
                                {{ form.project_type(class="form-control form-select") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-control form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.budget.label(class="form-label") }}
                                {{ form.budget(class="form-control") }}
                                {% if form.budget.errors %}
                                    {% for error in form.budget.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.area.label(class="form-label") }}
                                {{ form.area(class="form-control") }}
                                {% if form.area.errors %}
                                    {% for error in form.area.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.floors.label(class="form-label") }}
                                {{ form.floors(class="form-control") }}
                                {% if form.floors.errors %}
                                    {% for error in form.floors.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div style="display: flex; align-items: center;">
                            {{ form.is_public(style="margin-left: 0.5rem;") }}
                            {{ form.is_public.label() }}
                        </div>
                        <small class="form-text text-muted">
                            إذا كان المشروع عاماً، يمكن للعملاء رؤيته في قائمة المشاريع
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg mr-2") }}
                        <a href="{{ url_for('projects.index') }}" class="btn btn-secondary btn-lg">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
