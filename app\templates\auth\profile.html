{% extends "base/base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3>الملف الشخصي</h3>
                <p style="margin: 0; color: #6c757d;">إدارة معلومات حسابك الشخصي</p>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <form method="POST">
                            {{ form.hidden_tag() }}
                            
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    {% for error in form.name.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                    {% for error in form.email.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                {{ form.submit(class="btn btn-primary") }}
                                <a href="{{ url_for('auth.change_password') }}" class="btn btn-secondary">تغيير كلمة المرور</a>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>معلومات الحساب</h5>
                                <p><strong>الدور:</strong> {{ current_user.get_role_display() }}</p>
                                <p><strong>تاريخ التسجيل:</strong><br>{{ current_user.created_at.strftime('%Y/%m/%d') }}</p>
                                <p><strong>حالة الحساب:</strong><br>
                                    <span class="badge" style="background-color: {{ '#28a745' if current_user.is_active else '#dc3545' }}; color: white;">
                                        {{ 'نشط' if current_user.is_active else 'معطل' }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
