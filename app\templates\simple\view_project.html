{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>{{ project.name }}</h1>
        <p class="text-muted">{{ project.get_type_display() }} - {{ project.get_status_display() }}</p>
    </div>
    <div class="col-md-4 text-left">
        <a href="{{ url_for('projects') }}" class="btn btn-secondary">العودة للمشاريع</a>
    </div>
</div>

<!-- Project Details -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>تفاصيل المشروع</h5>
            </div>
            <div class="card-body">
                {% if project.description %}
                    <p><strong>الوصف:</strong></p>
                    <p>{{ project.description }}</p>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الموقع:</strong> {{ project.location or 'لم يتم تحديده' }}</p>
                        <p><strong>نوع المشروع:</strong> {{ project.get_type_display() }}</p>
                        <p><strong>حالة المشروع:</strong> {{ project.get_status_display() }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المساحة:</strong> {{ "{:,.0f}".format(project.area) if project.area else "غير محدد" }} {% if project.area %}م²{% endif %}</p>
                        <p><strong>عدد الطوابق:</strong> {{ project.floors }}</p>
                        <p><strong>الميزانية:</strong> {{ "{:,.2f}".format(project.budget) if project.budget else "غير محدد" }} {% if project.budget %}ريال{% endif %}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ project.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>آخر تحديث:</strong> {{ project.updated_at.strftime('%Y/%m/%d %H:%M') if project.updated_at else project.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>إحصائيات المشروع</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <h3 style="color: var(--primary-color);">0</h3>
                    <p class="text-muted">عدد الرسوم</p>
                </div>
                <div class="mb-3">
                    <h3 style="color: var(--accent-color);">0</h3>
                    <p class="text-muted">عدد الحسابات</p>
                </div>
                <div class="mb-3">
                    <p class="text-muted">المالك:</p>
                    <p><strong>{{ project.owner.name }}</strong></p>
                    <small class="text-muted">{{ project.owner.get_role_display() }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>الإجراءات السريعة</h5>
            </div>
            <div class="card-body text-center">
                <a href="#" class="btn btn-primary mr-2">إنشاء رسم جديد</a>
                <a href="#" class="btn btn-secondary mr-2">إنشاء حساب جديد</a>
                <a href="#" class="btn btn-info mr-2">تصدير تقرير PDF</a>
                <a href="{{ url_for('projects') }}" class="btn btn-outline-primary">العودة للمشاريع</a>
            </div>
        </div>
    </div>
</div>

<!-- Future Features -->
<div class="row">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>الميزات القادمة</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">سيتم إضافة الميزات التالية في الإصدارات القادمة:</p>
                <ul>
                    <li>رسوم ثنائية الأبعاد باستخدام Fabric.js</li>
                    <li>تصور ثلاثي الأبعاد باستخدام Three.js</li>
                    <li>حساب الكميات والتكاليف</li>
                    <li>تصدير ملفات PDF و DXF</li>
                    <li>إدارة المستخدمين والصلاحيات</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
