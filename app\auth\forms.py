# -*- coding: utf-8 -*-
"""
Authentication Forms
نماذج المصادقة
"""

from wtforms import Form
from wtforms import StringField, PasswordField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from models import User

class LoginForm(Form):
    """Login form - نموذج تسجيل الدخول"""
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='يرجى إدخال بريد إلكتروني صحيح')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='كلمة المرور مطلوبة')
    ])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegistrationForm(Form):
    """Registration form - نموذج التسجيل"""
    name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='يرجى إدخال بريد إلكتروني صحيح')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='كلمة المرور مطلوبة'),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    password2 = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(message='تأكيد كلمة المرور مطلوب'),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    role = SelectField('الدور', choices=[
        ('Engineer', 'مهندس'),
        ('Supervisor', 'مشرف'),
        ('Client', 'عميل')
    ], default='Engineer', validators=[DataRequired(message='الدور مطلوب')])
    submit = SubmitField('إنشاء حساب')
    
    def validate_email(self, email):
        """Check if email is already registered"""
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('هذا البريد الإلكتروني مسجل بالفعل. يرجى استخدام بريد آخر.')

class ChangePasswordForm(FlaskForm):
    """Change password form - نموذج تغيير كلمة المرور"""
    current_password = PasswordField('كلمة المرور الحالية', validators=[
        DataRequired(message='كلمة المرور الحالية مطلوبة')
    ])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[
        DataRequired(message='كلمة المرور الجديدة مطلوبة'),
        Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    ])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة', validators=[
        DataRequired(message='تأكيد كلمة المرور مطلوب'),
        EqualTo('new_password', message='كلمات المرور غير متطابقة')
    ])
    submit = SubmitField('تغيير كلمة المرور')

class ProfileForm(FlaskForm):
    """Profile update form - نموذج تحديث الملف الشخصي"""
    name = StringField('الاسم الكامل', validators=[
        DataRequired(message='الاسم مطلوب'),
        Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(message='البريد الإلكتروني مطلوب'),
        Email(message='يرجى إدخال بريد إلكتروني صحيح')
    ])
    submit = SubmitField('حفظ التغييرات')
    
    def __init__(self, original_email, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        """Check if email is already registered by another user"""
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('هذا البريد الإلكتروني مسجل بالفعل. يرجى استخدام بريد آخر.')
