#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع المعمارية - نسخة مبسطة
Architectural Project Management System - Simple Version
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///architectural_pm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='Engineer')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    projects = db.relationship('Project', backref='owner', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_role_display(self):
        role_names = {
            'Engineer': 'مهندس',
            'Supervisor': 'مشرف',
            'Client': 'عميل'
        }
        return role_names.get(self.role, self.role)

class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    location = db.Column(db.String(300))
    description = db.Column(db.Text)
    project_type = db.Column(db.String(50), default='Residential')
    status = db.Column(db.String(50), default='Planning')
    budget = db.Column(db.Numeric(15, 2))
    area = db.Column(db.Numeric(10, 2))
    floors = db.Column(db.Integer, default=1)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_type_display(self):
        type_names = {
            'Residential': 'سكني',
            'Commercial': 'تجاري',
            'Industrial': 'صناعي',
            'Mixed': 'مختلط'
        }
        return type_names.get(self.project_type, self.project_type)
    
    def get_status_display(self):
        status_names = {
            'Planning': 'تخطيط',
            'Design': 'تصميم',
            'Construction': 'إنشاء',
            'Completed': 'مكتمل',
            'On Hold': 'معلق'
        }
        return status_names.get(self.status, self.status)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('simple/index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash(f'مرحباً {user.name}! تم تسجيل الدخول بنجاح.', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('البريد الإلكتروني أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('simple/login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        password = request.form['password']
        role = request.form['role']
        
        # Check if user exists
        if User.query.filter_by(email=email).first():
            flash('هذا البريد الإلكتروني مسجل بالفعل.', 'error')
            return render_template('simple/register.html')
        
        # Create new user
        user = User(name=name, email=email, role=role)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.', 'success')
        return redirect(url_for('login'))
    
    return render_template('simple/register.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    projects = current_user.projects.all()
    return render_template('simple/dashboard.html', projects=projects)

@app.route('/projects')
@login_required
def projects():
    user_projects = current_user.projects.all()
    return render_template('simple/projects.html', projects=user_projects)

@app.route('/projects/create', methods=['GET', 'POST'])
@login_required
def create_project():
    if request.method == 'POST':
        name = request.form['name']
        location = request.form.get('location', '')
        description = request.form.get('description', '')
        project_type = request.form['project_type']
        
        project = Project(
            name=name,
            location=location,
            description=description,
            project_type=project_type,
            user_id=current_user.id
        )
        
        db.session.add(project)
        db.session.commit()
        
        flash(f'تم إنشاء المشروع "{name}" بنجاح!', 'success')
        return redirect(url_for('projects'))
    
    return render_template('simple/create_project.html')

@app.route('/projects/<int:id>')
@login_required
def view_project(id):
    project = Project.query.get_or_404(id)
    if project.user_id != current_user.id:
        flash('ليس لديك صلاحية لعرض هذا المشروع.', 'error')
        return redirect(url_for('projects'))

    return render_template('simple/view_project.html', project=project)

@app.route('/drawing')
@login_required
def drawing():
    return render_template('simple/drawing.html')

# Create database tables
with app.app_context():
    db.create_all()
    
    # Create admin user if not exists
    admin = User.query.filter_by(email='<EMAIL>').first()
    if not admin:
        admin = User(name='مدير النظام', email='<EMAIL>', role='Supervisor')
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("Admin user created: <EMAIL> / admin123")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
