<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    {% if title %}
        <title>{{ title }} - نظام إدارة المشاريع المعمارية</title>
    {% else %}
        <title>نظام إدارة المشاريع المعمارية</title>
    {% endif %}
    
    <!-- Arabic RTL CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/arabic-rtl.css') }}">
    
    <!-- Additional CSS -->
    {% block styles %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="row" style="width: 100%; align-items: center;">
                <div class="col">
                    <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                        🏗️ نظام إدارة المشاريع المعمارية
                    </a>
                </div>
                <div class="col" style="flex: 0 0 auto;">
                    <ul class="navbar-nav" style="flex-direction: row;">
                        {% if current_user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.dashboard') }}">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('projects.index') }}">المشاريع</a>
                            </li>
                            {% if current_user.role in ['Engineer', 'Supervisor'] %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('drawings.index') }}">الرسوم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('calculations.index') }}">الحسابات</a>
                            </li>
                            {% endif %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.profile') }}">{{ current_user.name }}</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.login') }}">تسجيل الدخول</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('auth.register') }}">إنشاء حساب</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="mt-5" style="background-color: var(--light-color); padding: 2rem 0; border-top: 1px solid var(--border-color);">
        <div class="container">
            <div class="row">
                <div class="col text-center">
                    <p style="margin: 0; color: var(--dark-color);">
                        © 2025 نظام إدارة المشاريع المعمارية. جميع الحقوق محفوظة.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 300);
            });
        }, 5000);

        // Close alert manually
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-close')) {
                const alert = e.target.closest('.alert');
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 300);
            }
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
