{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-8">
        <h1>التصور ثلاثي الأبعاد</h1>
        <p class="text-muted">عرض تفاعلي ثلاثي الأبعاد للمشاريع المعمارية</p>
    </div>
    <div class="col-md-4 text-left">
        <button class="btn btn-primary" onclick="loadFromDrawing()">تحميل من الرسم</button>
        <button class="btn btn-success" onclick="exportImage()">تصدير صورة</button>
    </div>
</div>

<!-- 3D Viewer Controls -->
<div class="card mb-3">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="setViewMode('isometric')">
                        🎯 منظور متساوي القياس
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="setViewMode('top')">
                        ⬆️ منظور علوي
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="setViewMode('front')">
                        ➡️ منظور أمامي
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="setViewMode('side')">
                        ↗️ منظور جانبي
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-left">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleLighting()">
                        💡 الإضاءة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleShadows()">
                        🌑 الظلال
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetView()">
                        🔄 إعادة تعيين
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="clearScene()">
                        🗑️ مسح
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 3D Viewer and Controls -->
<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-body" style="padding: 0;">
                <div id="threejs-container" style="width: 100%; height: 600px; position: relative;">
                    <div id="loading-indicator" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل المشهد ثلاثي الأبعاد...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Control Panel -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6>إعدادات العرض</h6>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">ارتفاع الطابق (م)</label>
                    <input type="number" id="floor-height" class="form-control" value="3" min="2" max="5" step="0.1" onchange="updateFloorHeight()">
                </div>
                
                <div class="form-group">
                    <label class="form-label">مقياس الرسم</label>
                    <select id="scale-factor" class="form-control" onchange="updateScale()">
                        <option value="0.05">1:20</option>
                        <option value="0.02">1:50</option>
                        <option value="0.01" selected>1:100</option>
                        <option value="0.005">1:200</option>
                    </select>
                </div>
                
                <hr>
                
                <h6>ألوان المواد</h6>
                <div class="form-group">
                    <label class="form-label">الجدران</label>
                    <input type="color" id="wall-color" class="form-control" value="#8B7355" onchange="updateMaterialColor('wall', this.value)">
                </div>
                
                <div class="form-group">
                    <label class="form-label">الأرضية</label>
                    <input type="color" id="floor-color" class="form-control" value="#F5F5DC" onchange="updateMaterialColor('floor', this.value)">
                </div>
                
                <div class="form-group">
                    <label class="form-label">السقف</label>
                    <input type="color" id="ceiling-color" class="form-control" value="#FFFFFF" onchange="updateMaterialColor('ceiling', this.value)">
                </div>
                
                <div class="form-group">
                    <label class="form-label">الأبواب</label>
                    <input type="color" id="door-color" class="form-control" value="#8B4513" onchange="updateMaterialColor('door', this.value)">
                </div>
                
                <div class="form-group">
                    <label class="form-label">النوافذ</label>
                    <input type="color" id="window-color" class="form-control" value="#87CEEB" onchange="updateMaterialColor('window', this.value)">
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>معلومات المشهد</h6>
            </div>
            <div class="card-body">
                <div id="scene-info">
                    <small class="text-muted">
                        <div>العناصر: <span id="mesh-count">0</span></div>
                        <div>المثلثات: <span id="triangle-count">0</span></div>
                        <div>الرؤوس: <span id="vertex-count">0</span></div>
                        <div>استدعاءات الرسم: <span id="draw-calls">0</span></div>
                    </small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>التحكم في الكاميرا</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <div><strong>الماوس الأيسر:</strong> دوران</div>
                    <div><strong>الماوس الأوسط:</strong> تحريك</div>
                    <div><strong>العجلة:</strong> تكبير/تصغير</div>
                    <div><strong>الماوس الأيمن:</strong> تحريك</div>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h6>تعليمات الاستخدام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>التحميل:</h6>
                        <ul class="small">
                            <li>انقر على "تحميل من الرسم" لتحويل الرسم ثنائي الأبعاد</li>
                            <li>يتم تحويل الجدران والغرف والأبواب تلقائياً</li>
                            <li>يمكن تعديل الألوان والإعدادات</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>وضع العرض:</h6>
                        <ul class="small">
                            <li><strong>متساوي القياس:</strong> منظور ثلاثي الأبعاد</li>
                            <li><strong>علوي:</strong> منظور من الأعلى</li>
                            <li><strong>أمامي:</strong> منظور من الأمام</li>
                            <li><strong>جانبي:</strong> منظور من الجانب</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>التحكم:</h6>
                        <ul class="small">
                            <li>استخدم الماوس للتنقل في المشهد</li>
                            <li>يمكن تغيير ألوان المواد</li>
            <li>تعديل ارتفاع الطوابق</li>
                            <li>تبديل الإضاءة والظلال</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>التصدير:</h6>
                        <ul class="small">
                            <li>تصدير المشهد كصورة PNG</li>
                            <li>حفظ الإعدادات المخصصة</li>
                            <li>طباعة المناظر المختلفة</li>
                            <li>مشاركة النماذج ثلاثية الأبعاد</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Three.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

<!-- 3D Viewer Script -->
<script src="{{ url_for('static', filename='js/3d-viewer.js') }}"></script>

<script>
// Initialize 3D viewer
let viewer3D;

document.addEventListener('DOMContentLoaded', function() {
    // Hide loading indicator after a short delay
    setTimeout(() => {
        document.getElementById('loading-indicator').style.display = 'none';
        
        // Initialize 3D viewer
        viewer3D = new ThreeDViewer('threejs-container');
        
        // Update scene info periodically
        setInterval(updateSceneInfo, 1000);
        
        // Create a sample scene
        createSampleScene();
    }, 1000);
});

function createSampleScene() {
    // Create sample 2D objects to demonstrate 3D conversion
    const sampleObjects = [
        {
            objectType: 'room',
            left: 100,
            top: 100,
            getScaledWidth: () => 400,
            getScaledHeight: () => 300
        },
        {
            objectType: 'wall',
            x1: 100, y1: 100, x2: 500, y2: 100
        },
        {
            objectType: 'wall',
            x1: 500, y1: 100, x2: 500, y2: 400
        },
        {
            objectType: 'wall',
            x1: 500, y1: 400, x2: 100, y2: 400
        },
        {
            objectType: 'wall',
            x1: 100, y1: 400, x2: 100, y2: 100
        },
        {
            objectType: 'door',
            left: 250,
            top: 95,
            getScaledWidth: () => 80,
            getScaledHeight: () => 10
        },
        {
            objectType: 'window',
            left: 495,
            top: 200,
            getScaledWidth: () => 10,
            getScaledHeight: () => 100
        }
    ];
    
    viewer3D.convertFrom2D(sampleObjects);
}

function loadFromDrawing() {
    // Check if drawing canvas exists (from drawing page)
    if (typeof drawingCanvas !== 'undefined') {
        const objects = drawingCanvas.canvas.getObjects().filter(obj => !obj.excludeFromExport);
        if (objects.length === 0) {
            alert('لا توجد عناصر في الرسم لتحويلها');
            return;
        }
        viewer3D.convertFrom2D(objects);
        updateSceneInfo();
    } else {
        alert('يجب أن تكون في صفحة الرسم أولاً لتحميل الرسم');
    }
}

function setViewMode(mode) {
    viewer3D.setViewMode(mode);
}

function toggleLighting() {
    viewer3D.toggleLighting();
}

function toggleShadows() {
    viewer3D.toggleShadows();
}

function resetView() {
    viewer3D.fitCameraToScene();
}

function clearScene() {
    if (confirm('هل أنت متأكد من مسح المشهد؟')) {
        viewer3D.clearScene();
        updateSceneInfo();
    }
}

function updateFloorHeight() {
    const height = parseFloat(document.getElementById('floor-height').value);
    viewer3D.floorHeight = height;
    // Re-convert current scene with new height
    if (typeof drawingCanvas !== 'undefined') {
        loadFromDrawing();
    }
}

function updateScale() {
    const scale = parseFloat(document.getElementById('scale-factor').value);
    viewer3D.scale = scale;
    // Re-convert current scene with new scale
    if (typeof drawingCanvas !== 'undefined') {
        loadFromDrawing();
    }
}

function updateMaterialColor(materialType, color) {
    const hexColor = parseInt(color.replace('#', ''), 16);
    viewer3D.setMaterial(materialType, hexColor);
}

function updateSceneInfo() {
    const info = viewer3D.getSceneInfo();
    document.getElementById('mesh-count').textContent = info.meshCount;
    document.getElementById('triangle-count').textContent = info.triangles;
    document.getElementById('vertex-count').textContent = info.vertices;
    document.getElementById('draw-calls').textContent = info.calls;
}

function exportImage() {
    const dataURL = viewer3D.exportAsImage();
    
    // Create download link
    const a = document.createElement('a');
    a.href = dataURL;
    a.download = '3d-model.png';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    alert('تم تصدير الصورة بنجاح!');
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    switch(e.key) {
        case '1':
            setViewMode('isometric');
            break;
        case '2':
            setViewMode('top');
            break;
        case '3':
            setViewMode('front');
            break;
        case '4':
            setViewMode('side');
            break;
        case 'l':
        case 'L':
            toggleLighting();
            break;
        case 's':
        case 'S':
            if (e.ctrlKey) {
                e.preventDefault();
                exportImage();
            } else {
                toggleShadows();
            }
            break;
        case 'r':
        case 'R':
            resetView();
            break;
    }
});
</script>
{% endblock %}
