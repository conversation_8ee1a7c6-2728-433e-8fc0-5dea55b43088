# -*- coding: utf-8 -*-
"""
Project Model
نموذج المشروع
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from app import db

class Project(db.Model):
    """
    Project model for managing architectural projects
    نموذج المشروع لإدارة المشاريع المعمارية
    """
    __tablename__ = 'projects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    location = db.Column(db.String(300), nullable=True)
    description = db.Column(db.Text, nullable=True)
    project_type = db.Column(db.Enum('Residential', 'Commercial', 'Industrial', 'Mixed', 
                                   name='project_types'), default='Residential')
    status = db.Column(db.Enum('Planning', 'Design', 'Construction', 'Completed', 'On Hold',
                              name='project_status'), default='Planning')
    budget = db.Column(db.Numeric(15, 2), nullable=True)
    area = db.Column(db.Numeric(10, 2), nullable=True)  # Total area in square meters
    floors = db.Column(db.Integer, default=1)
    is_public = db.Column(db.Boolean, default=False)  # Can clients view this project
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    drawings = db.relationship('Drawing', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    calculations = db.relationship('Calculation', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, name, user_id, location=None, description=None, project_type='Residential'):
        """Initialize project"""
        self.name = name
        self.user_id = user_id
        self.location = location
        self.description = description
        self.project_type = project_type
    
    def get_type_display(self):
        """Get Arabic project type display name"""
        type_names = {
            'Residential': 'سكني',
            'Commercial': 'تجاري',
            'Industrial': 'صناعي',
            'Mixed': 'مختلط'
        }
        return type_names.get(self.project_type, self.project_type)
    
    def get_status_display(self):
        """Get Arabic status display name"""
        status_names = {
            'Planning': 'تخطيط',
            'Design': 'تصميم',
            'Construction': 'إنشاء',
            'Completed': 'مكتمل',
            'On Hold': 'معلق'
        }
        return status_names.get(self.status, self.status)
    
    def get_latest_drawing(self):
        """Get the most recent drawing for this project"""
        return self.drawings.order_by(Drawing.created_at.desc()).first()
    
    def get_total_calculations(self):
        """Get total number of calculations for this project"""
        return self.calculations.count()
    
    def get_progress_percentage(self):
        """Calculate project progress based on status"""
        progress_map = {
            'Planning': 10,
            'Design': 30,
            'Construction': 70,
            'Completed': 100,
            'On Hold': 0
        }
        return progress_map.get(self.status, 0)
    
    def __repr__(self):
        return f'<Project {self.name}>'
    
    def to_dict(self):
        """Convert project to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'location': self.location,
            'description': self.description,
            'project_type': self.project_type,
            'project_type_display': self.get_type_display(),
            'status': self.status,
            'status_display': self.get_status_display(),
            'budget': float(self.budget) if self.budget else None,
            'area': float(self.area) if self.area else None,
            'floors': self.floors,
            'is_public': self.is_public,
            'user_id': self.user_id,
            'progress': self.get_progress_percentage(),
            'drawings_count': self.drawings.count(),
            'calculations_count': self.get_total_calculations(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
