# -*- coding: utf-8 -*-
"""
Drawing Model
نموذج الرسم
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json
from app import db

class Drawing(db.Model):
    """
    Drawing model for storing 2D floor plans and design data
    نموذج الرسم لحفظ المخططات ثنائية الأبعاد وبيانات التصميم
    """
    __tablename__ = 'drawings'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    drawing_data_json = db.Column(db.Text, nullable=False)  # Fabric.js canvas data
    version = db.Column(db.Integer, default=1, nullable=False)
    floor_level = db.Column(db.Integer, default=0)  # Ground floor = 0, basement = -1, etc.
    scale = db.Column(db.String(20), default='1:100')  # Drawing scale
    units = db.Column(db.Enum('meters', 'feet', 'centimeters', name='drawing_units'), default='meters')
    is_active = db.Column(db.Boolean, default=True)  # Current version flag
    thumbnail_path = db.Column(db.String(500), nullable=True)  # Path to thumbnail image
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, project_id, name, drawing_data_json, description=None, floor_level=0):
        """Initialize drawing"""
        self.project_id = project_id
        self.name = name
        self.drawing_data_json = drawing_data_json
        self.description = description
        self.floor_level = floor_level
    
    def get_drawing_data(self):
        """Parse and return drawing data as Python object"""
        try:
            return json.loads(self.drawing_data_json)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def set_drawing_data(self, data):
        """Set drawing data from Python object"""
        self.drawing_data_json = json.dumps(data, ensure_ascii=False)
    
    def get_floor_display(self):
        """Get Arabic floor level display"""
        if self.floor_level == 0:
            return 'الطابق الأرضي'
        elif self.floor_level > 0:
            return f'الطابق {self.floor_level}'
        else:
            return f'الطابق السفلي {abs(self.floor_level)}'
    
    def get_units_display(self):
        """Get Arabic units display"""
        units_names = {
            'meters': 'متر',
            'feet': 'قدم',
            'centimeters': 'سنتيمتر'
        }
        return units_names.get(self.units, self.units)
    
    def calculate_area(self):
        """Calculate total area from drawing data"""
        try:
            data = self.get_drawing_data()
            total_area = 0.0
            
            # Extract area calculations from drawing objects
            if 'objects' in data:
                for obj in data['objects']:
                    if obj.get('type') == 'room' and 'area' in obj:
                        total_area += float(obj['area'])
            
            return total_area
        except (ValueError, TypeError, KeyError):
            return 0.0
    
    def extract_rooms(self):
        """Extract room information from drawing data"""
        try:
            data = self.get_drawing_data()
            rooms = []
            
            if 'objects' in data:
                for obj in data['objects']:
                    if obj.get('type') == 'room':
                        room_info = {
                            'name': obj.get('name', 'غرفة بدون اسم'),
                            'area': obj.get('area', 0),
                            'perimeter': obj.get('perimeter', 0),
                            'width': obj.get('width', 0),
                            'height': obj.get('height', 0)
                        }
                        rooms.append(room_info)
            
            return rooms
        except (json.JSONDecodeError, TypeError, KeyError):
            return []
    
    def create_new_version(self):
        """Create a new version of this drawing"""
        # Mark current version as inactive
        self.is_active = False
        db.session.commit()
        
        # Create new version
        new_drawing = Drawing(
            project_id=self.project_id,
            name=self.name,
            drawing_data_json=self.drawing_data_json,
            description=self.description,
            floor_level=self.floor_level
        )
        new_drawing.version = self.version + 1
        new_drawing.scale = self.scale
        new_drawing.units = self.units
        
        return new_drawing
    
    def __repr__(self):
        return f'<Drawing {self.name} v{self.version}>'
    
    def to_dict(self):
        """Convert drawing to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'project_id': self.project_id,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'floor_level': self.floor_level,
            'floor_display': self.get_floor_display(),
            'scale': self.scale,
            'units': self.units,
            'units_display': self.get_units_display(),
            'is_active': self.is_active,
            'thumbnail_path': self.thumbnail_path,
            'total_area': self.calculate_area(),
            'rooms_count': len(self.extract_rooms()),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
