{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>مرحباً {{ current_user.name }}</h1>
        <p class="text-muted">{{ current_user.get_role_display() }} - لوحة التحكم الرئيسية</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ projects|length }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي المشاريع</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">0</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي الرسوم</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">0</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي الحسابات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ projects|sum(attribute='area')|round|int if projects else 0 }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي المساحة (م²)</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Projects -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">المشاريع الحديثة</h5>
            </div>
            <div class="card-body">
                {% if projects %}
                    {% for project in projects[:5] %}
                        <div class="mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h6 style="margin: 0;">
                                        <a href="{{ url_for('view_project', id=project.id) }}">{{ project.name }}</a>
                                    </h6>
                                    <small class="text-muted">{{ project.location or 'لم يتم تحديد الموقع' }}</small>
                                </div>
                                <div style="text-align: left;">
                                    <span class="badge" style="
                                        background-color: 
                                        {% if project.status == 'Completed' %}#28a745
                                        {% elif project.status == 'Construction' %}#ffc107
                                        {% elif project.status == 'Design' %}#17a2b8
                                        {% elif project.status == 'Planning' %}#6c757d
                                        {% else %}#dc3545{% endif %};
                                        color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">
                                        {{ project.get_status_display() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    <div class="text-center">
                        <a href="{{ url_for('projects') }}" class="btn btn-primary">عرض جميع المشاريع</a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <p>لا توجد مشاريع حتى الآن</p>
                        <a href="{{ url_for('create_project') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">الإجراءات السريعة</h5>
            </div>
            <div class="card-body text-center">
                <a href="{{ url_for('create_project') }}" class="btn btn-primary mb-2" style="width: 100%;">إنشاء مشروع جديد</a>
                <a href="{{ url_for('projects') }}" class="btn btn-secondary mb-2" style="width: 100%;">عرض جميع المشاريع</a>
                <a href="#" class="btn btn-info mb-2" style="width: 100%;">إنشاء رسم جديد</a>
                <a href="#" class="btn btn-warning mb-2" style="width: 100%;">إنشاء حساب جديد</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
