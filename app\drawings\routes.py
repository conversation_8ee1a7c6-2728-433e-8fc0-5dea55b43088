# -*- coding: utf-8 -*-
"""
Drawing Routes (Placeholder)
مسارات الرسوم (مؤقت)
"""

from flask import render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from app.drawings import bp

@bp.route('/')
@login_required
def index():
    """Drawings list - قائمة الرسوم"""
    flash('صفحة الرسوم قيد التطوير', 'info')
    return redirect(url_for('main.dashboard'))

@bp.route('/create')
@login_required
def create():
    """Create new drawing - إنشاء رسم جديد"""
    flash('إنشاء الرسوم قيد التطوير', 'info')
    return redirect(url_for('main.dashboard'))

@bp.route('/<int:id>')
@login_required
def view(id):
    """View drawing - عرض الرسم"""
    flash('عرض الرسوم قيد التطوير', 'info')
    return redirect(url_for('main.dashboard'))
