/**
 * Quantity Calculation Engine
 * محرك حساب الكميات
 */

class QuantityCalculator {
    constructor() {
        this.calculations = [];
        this.unitPrices = {
            // أسعار المواد بالريال السعودي
            'concrete_m3': 350,      // خرسانة - متر مكعب
            'steel_kg': 4.5,         // حديد تسليح - كيلوجرام
            'brick_m2': 45,          // طوب - متر مربع
            'tiles_m2': 80,          // بلاط - متر مربع
            'paint_m2': 25,          // دهان - متر مربع
            'cement_bag': 35,        // أسمنت - كيس
            'sand_m3': 120,          // رمل - متر مكعب
            'gravel_m3': 140         // حصى - متر مكعب
        };
        
        this.materialFactors = {
            // معاملات حساب المواد
            'concrete_foundation': 0.15,    // سماكة الأساس
            'concrete_slab': 0.20,          // سماكة البلاطة
            'steel_ratio': 120,             // كيلو حديد لكل متر مكعب خرسانة
            'brick_thickness': 0.20,        // سماكة الجدار
            'mortar_ratio': 0.25            // نسبة المونة
        };
    }
    
    // حساب المساحات من الرسم
    calculateAreasFromDrawing(canvasObjects) {
        const areas = {
            totalArea: 0,
            roomAreas: [],
            wallAreas: [],
            openingAreas: []
        };
        
        canvasObjects.forEach(obj => {
            if (obj.objectType === 'room') {
                const area = this.calculateRectangleArea(
                    obj.getScaledWidth(),
                    obj.getScaledHeight()
                );
                areas.roomAreas.push({
                    name: obj.name || 'غرفة',
                    area: area,
                    width: obj.getScaledWidth(),
                    height: obj.getScaledHeight()
                });
                areas.totalArea += area;
            }
            
            if (obj.objectType === 'wall') {
                const length = this.calculateLineLength(obj);
                const wallArea = length * 3; // افتراض ارتفاع 3 متر
                areas.wallAreas.push({
                    length: length,
                    area: wallArea,
                    height: 3
                });
            }
            
            if (obj.objectType === 'door' || obj.objectType === 'window') {
                const area = this.calculateRectangleArea(
                    obj.getScaledWidth(),
                    obj.getScaledHeight()
                );
                areas.openingAreas.push({
                    type: obj.objectType,
                    area: area,
                    width: obj.getScaledWidth(),
                    height: obj.getScaledHeight()
                });
            }
        });
        
        return areas;
    }
    
    // حساب مساحة المستطيل
    calculateRectangleArea(width, height) {
        // تحويل من البكسل إلى المتر (افتراض 1 بكسل = 0.05 متر)
        const widthM = (width * 0.05);
        const heightM = (height * 0.05);
        return widthM * heightM;
    }
    
    // حساب طول الخط
    calculateLineLength(lineObj) {
        const x1 = lineObj.x1;
        const y1 = lineObj.y1;
        const x2 = lineObj.x2;
        const y2 = lineObj.y2;
        
        const lengthPx = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        return lengthPx * 0.05; // تحويل إلى متر
    }
    
    // حساب كميات المواد
    calculateMaterialQuantities(areas, floors = 1) {
        const quantities = {};
        
        // حساب الخرسانة
        const foundationVolume = areas.totalArea * this.materialFactors.concrete_foundation;
        const slabVolume = areas.totalArea * this.materialFactors.concrete_slab * floors;
        const totalConcreteVolume = foundationVolume + slabVolume;
        
        quantities.concrete = {
            foundation: foundationVolume,
            slabs: slabVolume,
            total: totalConcreteVolume,
            unit: 'متر مكعب',
            unitPrice: this.unitPrices.concrete_m3,
            totalCost: totalConcreteVolume * this.unitPrices.concrete_m3
        };
        
        // حساب الحديد
        const steelWeight = totalConcreteVolume * this.materialFactors.steel_ratio;
        quantities.steel = {
            weight: steelWeight,
            unit: 'كيلوجرام',
            unitPrice: this.unitPrices.steel_kg,
            totalCost: steelWeight * this.unitPrices.steel_kg
        };
        
        // حساب الطوب
        const wallArea = areas.wallAreas.reduce((sum, wall) => sum + wall.area, 0);
        const openingArea = areas.openingAreas.reduce((sum, opening) => sum + opening.area, 0);
        const netWallArea = (wallArea - openingArea) * floors;
        
        quantities.brick = {
            area: netWallArea,
            unit: 'متر مربع',
            unitPrice: this.unitPrices.brick_m2,
            totalCost: netWallArea * this.unitPrices.brick_m2
        };
        
        // حساب البلاط
        const tilesArea = areas.totalArea * floors;
        quantities.tiles = {
            area: tilesArea,
            unit: 'متر مربع',
            unitPrice: this.unitPrices.tiles_m2,
            totalCost: tilesArea * this.unitPrices.tiles_m2
        };
        
        // حساب الدهان
        const paintArea = (netWallArea + (areas.totalArea * floors * 2)); // جدران + سقف وأرضية
        quantities.paint = {
            area: paintArea,
            unit: 'متر مربع',
            unitPrice: this.unitPrices.paint_m2,
            totalCost: paintArea * this.unitPrices.paint_m2
        };
        
        // حساب الأسمنت
        const cementBags = Math.ceil(totalConcreteVolume * 7); // 7 أكياس لكل متر مكعب
        quantities.cement = {
            bags: cementBags,
            unit: 'كيس',
            unitPrice: this.unitPrices.cement_bag,
            totalCost: cementBags * this.unitPrices.cement_bag
        };
        
        // حساب الرمل
        const sandVolume = totalConcreteVolume * 0.6; // 60% من حجم الخرسانة
        quantities.sand = {
            volume: sandVolume,
            unit: 'متر مكعب',
            unitPrice: this.unitPrices.sand_m3,
            totalCost: sandVolume * this.unitPrices.sand_m3
        };
        
        // حساب الحصى
        const gravelVolume = totalConcreteVolume * 0.8; // 80% من حجم الخرسانة
        quantities.gravel = {
            volume: gravelVolume,
            unit: 'متر مكعب',
            unitPrice: this.unitPrices.gravel_m3,
            totalCost: gravelVolume * this.unitPrices.gravel_m3
        };
        
        return quantities;
    }
    
    // حساب التكلفة الإجمالية
    calculateTotalCost(quantities) {
        let totalCost = 0;
        Object.values(quantities).forEach(item => {
            if (item.totalCost) {
                totalCost += item.totalCost;
            }
        });
        return totalCost;
    }
    
    // إنشاء تقرير مفصل
    generateDetailedReport(areas, quantities, projectInfo = {}) {
        const totalCost = this.calculateTotalCost(quantities);
        
        const report = {
            projectInfo: {
                name: projectInfo.name || 'مشروع غير محدد',
                date: new Date().toLocaleDateString('ar-SA'),
                totalArea: areas.totalArea.toFixed(2),
                floors: projectInfo.floors || 1
            },
            areas: {
                total: areas.totalArea.toFixed(2),
                rooms: areas.roomAreas.map(room => ({
                    name: room.name,
                    area: room.area.toFixed(2),
                    dimensions: `${room.width.toFixed(1)} × ${room.height.toFixed(1)}`
                })),
                walls: areas.wallAreas.map((wall, index) => ({
                    id: index + 1,
                    length: wall.length.toFixed(2),
                    area: wall.area.toFixed(2)
                }))
            },
            quantities: {},
            costs: {
                subtotal: totalCost,
                vat: totalCost * 0.15, // ضريبة القيمة المضافة 15%
                total: totalCost * 1.15
            }
        };
        
        // تنسيق كميات المواد
        Object.keys(quantities).forEach(material => {
            const item = quantities[material];
            report.quantities[material] = {
                name: this.getMaterialNameArabic(material),
                quantity: this.getQuantityValue(item),
                unit: item.unit,
                unitPrice: item.unitPrice.toFixed(2),
                totalCost: item.totalCost.toFixed(2)
            };
        });
        
        return report;
    }
    
    // الحصول على اسم المادة بالعربية
    getMaterialNameArabic(material) {
        const names = {
            'concrete': 'خرسانة',
            'steel': 'حديد تسليح',
            'brick': 'طوب',
            'tiles': 'بلاط',
            'paint': 'دهان',
            'cement': 'أسمنت',
            'sand': 'رمل',
            'gravel': 'حصى'
        };
        return names[material] || material;
    }
    
    // الحصول على قيمة الكمية
    getQuantityValue(item) {
        if (item.total !== undefined) return item.total.toFixed(2);
        if (item.weight !== undefined) return item.weight.toFixed(0);
        if (item.area !== undefined) return item.area.toFixed(2);
        if (item.volume !== undefined) return item.volume.toFixed(2);
        if (item.bags !== undefined) return item.bags.toString();
        return '0';
    }
    
    // تصدير التقرير كـ JSON
    exportReportAsJSON(report) {
        return JSON.stringify(report, null, 2);
    }
    
    // تصدير التقرير كـ CSV
    exportReportAsCSV(report) {
        let csv = 'المادة,الكمية,الوحدة,سعر الوحدة,التكلفة الإجمالية\n';
        
        Object.values(report.quantities).forEach(item => {
            csv += `${item.name},${item.quantity},${item.unit},${item.unitPrice},${item.totalCost}\n`;
        });
        
        csv += `\nالمجموع الفرعي,,,,${report.costs.subtotal.toFixed(2)}\n`;
        csv += `ضريبة القيمة المضافة,,,,${report.costs.vat.toFixed(2)}\n`;
        csv += `المجموع الإجمالي,,,,${report.costs.total.toFixed(2)}\n`;
        
        return csv;
    }
    
    // حفظ الحساب
    saveCalculation(name, report) {
        const calculation = {
            id: Date.now(),
            name: name,
            date: new Date().toISOString(),
            report: report
        };
        
        this.calculations.push(calculation);
        this.saveToLocalStorage();
        return calculation;
    }
    
    // تحميل الحسابات المحفوظة
    loadCalculations() {
        const saved = localStorage.getItem('architectural_calculations');
        if (saved) {
            this.calculations = JSON.parse(saved);
        }
        return this.calculations;
    }
    
    // حفظ في التخزين المحلي
    saveToLocalStorage() {
        localStorage.setItem('architectural_calculations', JSON.stringify(this.calculations));
    }
    
    // حذف حساب
    deleteCalculation(id) {
        this.calculations = this.calculations.filter(calc => calc.id !== id);
        this.saveToLocalStorage();
    }
}
