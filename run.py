#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Run Script for Architectural Project Management System
سكريبت التشغيل السريع لنظام إدارة المشاريع المعمارية
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import werkzeug
        print("✓ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_database():
    """Check if database exists"""
    if os.path.exists('architectural_pm.db'):
        print("✓ Database found")
        return True
    else:
        print("✗ Database not found")
        print("Please run: python setup.py")
        return False

def open_browser():
    """Open browser after a delay"""
    time.sleep(2)  # Wait for server to start
    webbrowser.open('http://localhost:5000')

def main():
    """Main function"""
    print("=== Architectural Project Management System ===")
    print("=== نظام إدارة المشاريع المعمارية ===\n")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check database
    if not check_database():
        setup_choice = input("Would you like to run setup now? (y/n): ").lower()
        if setup_choice == 'y':
            try:
                subprocess.check_call([sys.executable, "setup.py"])
            except subprocess.CalledProcessError:
                print("Setup failed. Please run setup.py manually.")
                sys.exit(1)
        else:
            sys.exit(1)
    
    # Start the application
    print("Starting the application...")
    print("The application will open in your browser automatically.")
    print("If it doesn't open, go to: http://localhost:5000")
    print("\nDemo accounts:")
    print("Admin: <EMAIL> / admin123")
    print("Engineer: <EMAIL> / engineer123")
    print("Client: <EMAIL> / client123")
    print("\nPress Ctrl+C to stop the server\n")
    
    # Open browser in a separate thread
    Timer(2.0, open_browser).start()
    
    # Run the application
    try:
        subprocess.call([sys.executable, "simple_app.py"])
    except KeyboardInterrupt:
        print("\n\nServer stopped. Thank you for using the system!")
        print("تم إيقاف الخادم. شكراً لاستخدام النظام!")

if __name__ == '__main__':
    main()
