{% extends "base/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1>مرحباً {{ current_user.name }}</h1>
        <p class="text-muted">{{ current_user.get_role_display() }} - لوحة التحكم الرئيسية</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ data.total_projects }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي المشاريع</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ data.total_drawings }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي الرسوم</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ data.total_calculations }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي الحسابات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
            <div class="card-body">
                <h2 style="color: white; margin-bottom: 0.5rem;">{{ "{:,.0f}".format(data.total_area) if data.total_area else "0" }}</h2>
                <p style="margin: 0; opacity: 0.9;">إجمالي المساحة (م²)</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Projects -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">المشاريع الحديثة</h5>
            </div>
            <div class="card-body">
                {% if data.recent_projects %}
                    {% for project in data.recent_projects %}
                        <div class="mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h6 style="margin: 0;">
                                        <a href="{{ url_for('projects.view', id=project.id) }}">{{ project.name }}</a>
                                    </h6>
                                    <small class="text-muted">{{ project.location or 'لم يتم تحديد الموقع' }}</small>
                                </div>
                                <div style="text-align: left;">
                                    <span class="badge" style="
                                        background-color: 
                                        {% if project.status == 'Completed' %}#28a745
                                        {% elif project.status == 'Construction' %}#ffc107
                                        {% elif project.status == 'Design' %}#17a2b8
                                        {% elif project.status == 'Planning' %}#6c757d
                                        {% else %}#dc3545{% endif %};
                                        color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">
                                        {{ project.get_status_display() }}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div style="background-color: #f8f9fa; border-radius: 0.25rem; height: 0.5rem; overflow: hidden;">
                                    <div style="background-color: var(--primary-color); height: 100%; width: {{ project.get_progress_percentage() }}%; transition: width 0.3s ease;"></div>
                                </div>
                                <small class="text-muted">{{ project.get_progress_percentage() }}% مكتمل</small>
                            </div>
                        </div>
                    {% endfor %}
                    <div class="text-center">
                        <a href="{{ url_for('projects.index') }}" class="btn btn-primary">عرض جميع المشاريع</a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <p>لا توجد مشاريع حتى الآن</p>
                        <a href="{{ url_for('projects.create') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Project Status Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">توزيع حالة المشاريع</h5>
            </div>
            <div class="card-body">
                {% if data.status_counts %}
                    {% for status, count in data.status_counts.items() %}
                        <div class="mb-3">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <span>
                                    {% if status == 'Planning' %}تخطيط
                                    {% elif status == 'Design' %}تصميم
                                    {% elif status == 'Construction' %}إنشاء
                                    {% elif status == 'Completed' %}مكتمل
                                    {% elif status == 'On Hold' %}معلق
                                    {% else %}{{ status }}{% endif %}
                                </span>
                                <span class="badge" style="background-color: var(--primary-color); color: white;">{{ count }}</span>
                            </div>
                            <div style="background-color: #f8f9fa; border-radius: 0.25rem; height: 0.5rem; overflow: hidden;">
                                <div style="
                                    background-color: 
                                    {% if status == 'Completed' %}#28a745
                                    {% elif status == 'Construction' %}#ffc107
                                    {% elif status == 'Design' %}#17a2b8
                                    {% elif status == 'Planning' %}#6c757d
                                    {% else %}#dc3545{% endif %};
                                    height: 100%; 
                                    width: {{ (count / data.total_projects * 100) if data.total_projects > 0 else 0 }}%; 
                                    transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <p>لا توجد بيانات لعرضها</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">الرسوم الحديثة</h5>
            </div>
            <div class="card-body">
                {% if data.recent_drawings %}
                    {% for drawing in data.recent_drawings %}
                        <div class="mb-2 pb-2" style="border-bottom: 1px solid #eee;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h6 style="margin: 0;">
                                        <a href="{{ url_for('drawings.view', id=drawing.id) }}">{{ drawing.name }}</a>
                                    </h6>
                                    <small class="text-muted">{{ drawing.project.name }}</small>
                                </div>
                                <small class="text-muted">{{ drawing.created_at.strftime('%Y/%m/%d') }}</small>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <p>لا توجد رسوم حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">الحسابات الحديثة</h5>
            </div>
            <div class="card-body">
                {% if data.recent_calculations %}
                    {% for calc in data.recent_calculations %}
                        <div class="mb-2 pb-2" style="border-bottom: 1px solid #eee;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h6 style="margin: 0;">
                                        <a href="{{ url_for('calculations.view', id=calc.id) }}">{{ calc.name }}</a>
                                    </h6>
                                    <small class="text-muted">{{ calc.project.name }} - {{ calc.get_type_display() }}</small>
                                </div>
                                <small class="text-muted">{{ calc.created_at.strftime('%Y/%m/%d') }}</small>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted">
                        <p>لا توجد حسابات حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5 style="margin: 0;">الإجراءات السريعة</h5>
            </div>
            <div class="card-body text-center">
                <a href="{{ url_for('projects.create') }}" class="btn btn-primary mr-2">إنشاء مشروع جديد</a>
                {% if current_user.role in ['Engineer', 'Supervisor'] %}
                    <a href="{{ url_for('drawings.create') }}" class="btn btn-secondary mr-2">إنشاء رسم جديد</a>
                    <a href="{{ url_for('calculations.create') }}" class="btn btn-info mr-2">إنشاء حساب جديد</a>
                {% endif %}
                <a href="{{ url_for('main.help') }}" class="btn btn-outline-primary">المساعدة</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
