{% extends "base/base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header text-center">
                <h3>إنشاء حساب جديد</h3>
                <p style="margin: 0; color: #6c757d;">املأ البيانات التالية لإنشاء حسابك</p>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    {% for error in form.name.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                {% if form.email.errors %}
                                    {% for error in form.email.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                    {% for error in form.password.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.password2.label(class="form-label") }}
                                {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                                {% if form.password2.errors %}
                                    {% for error in form.password2.errors %}
                                        <div class="invalid-feedback">{{ error }}</div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.role.label(class="form-label") }}
                        {{ form.role(class="form-control form-select" + (" is-invalid" if form.role.errors else "")) }}
                        {% if form.role.errors %}
                            {% for error in form.role.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <small class="form-text text-muted">
                            اختر دورك في النظام: المهندس يمكنه إنشاء وتعديل المشاريع، المشرف يمكنه مراجعة جميع المشاريع، العميل يمكنه عرض المشاريع المخصصة له فقط.
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg", style="width: 100%;") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p style="margin: 0;">
                    لديك حساب بالفعل؟ 
                    <a href="{{ url_for('auth.login') }}">تسجيل الدخول</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
