{% extends "simple/base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>المشاريع</h1>
        <p class="text-muted">إدارة وعرض جميع المشاريع المعمارية</p>
    </div>
    <div class="col-md-4 text-left">
        <a href="{{ url_for('create_project') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
    </div>
</div>

<!-- Projects List -->
{% if projects %}
    <div class="row">
        {% for project in projects %}
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <h5 style="margin: 0;">
                                <a href="{{ url_for('view_project', id=project.id) }}">{{ project.name }}</a>
                            </h5>
                            <span class="badge" style="
                                background-color: 
                                {% if project.status == 'Completed' %}#28a745
                                {% elif project.status == 'Construction' %}#ffc107
                                {% elif project.status == 'Design' %}#17a2b8
                                {% elif project.status == 'Planning' %}#6c757d
                                {% else %}#dc3545{% endif %};
                                color: white;">
                                {{ project.get_status_display() }}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            <strong>الموقع:</strong> {{ project.location or 'لم يتم تحديده' }}
                        </p>
                        <p class="text-muted mb-2">
                            <strong>النوع:</strong> {{ project.get_type_display() }}
                        </p>
                        {% if project.description %}
                            <p class="mb-3">{{ project.description[:100] }}{% if project.description|length > 100 %}...{% endif %}</p>
                        {% endif %}
                        
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <small class="text-muted">المساحة</small>
                                <div><strong>{{ "{:,.0f}".format(project.area) if project.area else "غير محدد" }}</strong></div>
                                {% if project.area %}<small class="text-muted">م²</small>{% endif %}
                            </div>
                            <div class="col-4">
                                <small class="text-muted">الطوابق</small>
                                <div><strong>{{ project.floors }}</strong></div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">الميزانية</small>
                                <div><strong>{{ "{:,.0f}".format(project.budget) if project.budget else "غير محدد" }}</strong></div>
                                {% if project.budget %}<small class="text-muted">ريال</small>{% endif %}
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <a href="{{ url_for('view_project', id=project.id) }}" class="btn btn-primary btn-sm">عرض التفاصيل</a>
                        </div>
                    </div>
                    <div class="card-footer text-muted text-center">
                        <small>
                            آخر تحديث: {{ project.updated_at.strftime('%Y/%m/%d %H:%M') if project.updated_at else project.created_at.strftime('%Y/%m/%d %H:%M') }}
                        </small>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center">
        <div class="card">
            <div class="card-body">
                <h3 class="text-muted">لا توجد مشاريع</h3>
                <p class="text-muted">لم يتم إنشاء أي مشاريع بعد.</p>
                <a href="{{ url_for('create_project') }}" class="btn btn-primary">إنشاء مشروع جديد</a>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
